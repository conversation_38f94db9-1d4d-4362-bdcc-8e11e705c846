import React, { useState, useEffect } from "react";

export default function BasicInfoSection({ value = {}, onChange }) {
  const [basicInfo, setBasicInfo] = useState({
    title: value.title || "",
    serviceName: value.serviceName || "",
    date: value.date || new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (value && Object.keys(value).length) {
      setBasicInfo(value);
    }
  }, [value]);

  const handleChange = (field, newValue) => {
    const updatedInfo = { ...basicInfo, [field]: newValue };
    setBasicInfo(updatedInfo);
    onChange && onChange(updatedInfo);
  };

  return (
    <div className="section p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-3">Basic Information</h2>
      <p className="text-gray-600 mb-4">Enter the basic details for your proposal</p>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Proposal Title
          </label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            value={basicInfo.title}
            onChange={(e) => handleChange("title", e.target.value)}
            placeholder="Enter a title for your proposal"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Service Name
          </label>
          <input
            type="text"
            className="w-full p-2 border rounded"
            value={basicInfo.serviceName}
            onChange={(e) => handleChange("serviceName", e.target.value)}
            placeholder="What service are you proposing?"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date
          </label>
          <input
            type="date"
            className="w-full p-2 border rounded"
            value={basicInfo.date}
            onChange={(e) => handleChange("date", e.target.value)}
          />
        </div>
      </div>
    </div>
  );
}

