import React, { useState } from "react";
import SmartTextarea from "./SmartTextarea";

export default function MilestonesSection({ value = [], onChange }) {
  const [milestones, setMilestones] = useState(value.length ? value : [
    { title: "", description: "", deadline: "" }
  ]);

  const handleMilestoneChange = (index, field, newValue) => {
    const updatedMilestones = [...milestones];
    updatedMilestones[index][field] = newValue;
    setMilestones(updatedMilestones);
    onChange && onChange(updatedMilestones);
  };

  const addMilestone = () => {
    setMilestones([...milestones, { title: "", description: "", deadline: "" }]);
  };

  const removeMilestone = (index) => {
    const updatedMilestones = milestones.filter((_, i) => i !== index);
    setMilestones(updatedMilestones);
    onChange && onChange(updatedMilestones);
  };

  return (
    <div className="section p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-3">Project Milestones</h2>
      <p className="text-gray-600 mb-4">Define key project milestones and deadlines</p>
      
      <div className="overflow-x-auto">
        <table className="w-full border-collapse mb-4">
          <thead>
            <tr className="bg-gray-50">
              <th className="border p-2 text-left">Milestone</th>
              <th className="border p-2 text-left">Description</th>
              <th className="border p-2 text-left">Deadline</th>
              <th className="border p-2 text-left w-16">Actions</th>
            </tr>
          </thead>
          <tbody>
            {milestones.map((milestone, index) => (
              <tr key={index}>
                <td className="border p-2">
                  <input
                    type="text"
                    className="w-full p-2 border rounded"
                    value={milestone.title}
                    onChange={(e) => handleMilestoneChange(index, "title", e.target.value)}
                    placeholder="Milestone name"
                  />
                </td>
                <td className="border p-2">
                  <input
                    type="text"
                    className="w-full p-2 border rounded"
                    value={milestone.description}
                    onChange={(e) => handleMilestoneChange(index, "description", e.target.value)}
                    placeholder="Description"
                  />
                </td>
                <td className="border p-2">
                  <input
                    type="date"
                    className="w-full p-2 border rounded"
                    value={milestone.deadline}
                    onChange={(e) => handleMilestoneChange(index, "deadline", e.target.value)}
                  />
                </td>
                <td className="border p-2">
                  <button
                    onClick={() => removeMilestone(index)}
                    className="p-1 text-red-500 hover:text-red-700"
                    disabled={milestones.length === 1}
                  >
                    ✕
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <button
        onClick={addMilestone}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        Add Milestone
      </button>
    </div>
  );
}
