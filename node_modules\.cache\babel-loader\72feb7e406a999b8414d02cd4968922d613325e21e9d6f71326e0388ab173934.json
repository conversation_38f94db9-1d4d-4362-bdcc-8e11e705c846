{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\dhobdyjr\\\\ProposalPro\\\\src\\\\Pages\\\\CreateProposal.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport BasicInfoSection from \"../Components/BasicInfoSection\";\nimport CompanyInfoSection from \"../Components/CompanyInfoSection\";\nimport RecipientInfoSection from \"../Components/RecipientInfoSection\";\nimport ProjectDetailsSection from \"../Components/ProjectDetailsSection\";\nimport ServicesSection from \"../Components/ServicesSection\";\nimport MilestonesSection from \"../Components/MilestonesSection\";\nimport TermsSection from \"../Components/TermsSection\";\nimport ConclusionSection from \"../Components/ConclusionSection\";\nimport CoverImageSection from \"../Components/CoverImageSection\";\nimport Proposal from \"../Entities/Proposal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function CreateProposal() {\n  _s();\n  var _proposal$sections, _proposal$sections2, _proposal$sections3, _proposal$sections4, _proposal$sections5, _proposal$sections6, _proposal$sections7, _proposal$sections8, _proposal$sections9, _proposal$sections0, _proposal$sections0$c, _proposal$sections1, _proposal$sections1$c, _proposal$sections10, _proposal$sections10$, _proposal$sections11, _proposal$sections11$, _proposal$sections12, _proposal$sections12$, _proposal$sections13, _proposal$sections13$, _proposal$sections14, _proposal$sections14$, _proposal$sections15, _proposal$sections15$, _proposal$sections16, _proposal$sections16$, _proposal$sections17, _proposal$sections17$, _proposal$sections18, _proposal$sections18$, _proposal$sections19, _proposal$sections19$, _proposal$sections20, _proposal$sections20$, _proposal$sections21, _proposal$sections21$, _proposal$sections22, _proposal$sections22$, _proposal$sections23, _proposal$sections23$, _proposal$sections24, _proposal$sections24$, _proposal$sections25, _proposal$sections25$, _proposal$sections26, _proposal$sections27, _proposal$sections27$, _proposal$sections28, _proposal$sections28$, _proposal$sections29, _proposal$sections29$, _proposal$sections30, _proposal$sections30$, _proposal$sections31, _proposal$sections31$, _proposal$sections32, _proposal$sections32$;\n  const [proposal, setProposal] = useState(new Proposal());\n  const [activeTab, setActiveTab] = useState(\"edit\"); // \"edit\" or \"preview\"\n  const [showAIReviewModal, setShowAIReviewModal] = useState(false);\n  const [isReviewing, setIsReviewing] = useState(false);\n  const [reviewComplete, setReviewComplete] = useState(false);\n  const [aiSuggestions, setAiSuggestions] = useState(null);\n  const handleSectionChange = (sectionName, data) => {\n    setProposal(prev => {\n      const updated = {\n        ...prev\n      };\n      updated.sections = updated.sections || {};\n      updated.sections[sectionName] = data;\n      return updated;\n    });\n  };\n  const handleSave = () => {\n    // Show AI review confirmation modal\n    setShowAIReviewModal(true);\n  };\n  const handleAIReview = async () => {\n    setIsReviewing(true);\n    try {\n      // Simulate AI review process\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // In a real implementation, you would call your AI service here\n      // const response = await fetch('/api/ai-review', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(proposal)\n      // });\n      // const data = await response.json();\n\n      // Mock AI suggestions\n      const mockSuggestions = {\n        coverImage: {\n          prompt: \"A professional business proposal with abstract blue and purple gradient elements, featuring a handshake silhouette symbolizing partnership\",\n          rationale: \"This image conveys professionalism and partnership, aligning with your proposal's tone and purpose.\"\n        },\n        contentImprovements: [\"Consider adding more specific metrics in your milestones section to make progress more measurable.\", \"The introduction could benefit from a stronger value proposition statement.\", \"Your pricing structure might be more compelling with tiered options.\"]\n      };\n      setAiSuggestions(mockSuggestions);\n      setReviewComplete(true);\n    } catch (error) {\n      console.error(\"AI review failed:\", error);\n    } finally {\n      setIsReviewing(false);\n    }\n  };\n  const handleCloseModal = () => {\n    setShowAIReviewModal(false);\n    setReviewComplete(false);\n    setAiSuggestions(null);\n  };\n  const handleApplySuggestions = () => {\n    // Here you would apply the AI suggestions to your proposal\n    // For now, we'll just close the modal and save\n    handleCloseModal();\n    console.log(\"Saving proposal:\", proposal);\n    alert(\"Proposal saved successfully with AI recommendations!\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold\",\n        children: \"Create Proposal\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(\"edit\"),\n          className: `px-4 py-2 rounded ${activeTab === \"edit\" ? \"bg-blue-500 text-white\" : \"bg-gray-200\"}`,\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(\"preview\"),\n          className: `px-4 py-2 rounded ${activeTab === \"preview\" ? \"bg-blue-500 text-white\" : \"bg-gray-200\"}`,\n          children: \"Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSave,\n          className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n          children: \"Save Proposal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), activeTab === \"edit\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(BasicInfoSection, {\n        value: (_proposal$sections = proposal.sections) === null || _proposal$sections === void 0 ? void 0 : _proposal$sections.basicInfo,\n        onChange: data => handleSectionChange(\"basicInfo\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(CompanyInfoSection, {\n        value: (_proposal$sections2 = proposal.sections) === null || _proposal$sections2 === void 0 ? void 0 : _proposal$sections2.companyInfo,\n        onChange: data => handleSectionChange(\"companyInfo\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(RecipientInfoSection, {\n        value: (_proposal$sections3 = proposal.sections) === null || _proposal$sections3 === void 0 ? void 0 : _proposal$sections3.recipientInfo,\n        onChange: data => handleSectionChange(\"recipientInfo\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ProjectDetailsSection, {\n        value: (_proposal$sections4 = proposal.sections) === null || _proposal$sections4 === void 0 ? void 0 : _proposal$sections4.projectDetails,\n        onChange: data => handleSectionChange(\"projectDetails\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ServicesSection, {\n        value: (_proposal$sections5 = proposal.sections) === null || _proposal$sections5 === void 0 ? void 0 : _proposal$sections5.services,\n        onChange: data => handleSectionChange(\"services\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(MilestonesSection, {\n        value: (_proposal$sections6 = proposal.sections) === null || _proposal$sections6 === void 0 ? void 0 : _proposal$sections6.milestones,\n        onChange: data => handleSectionChange(\"milestones\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TermsSection, {\n        value: (_proposal$sections7 = proposal.sections) === null || _proposal$sections7 === void 0 ? void 0 : _proposal$sections7.terms,\n        onChange: data => handleSectionChange(\"terms\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ConclusionSection, {\n        value: (_proposal$sections8 = proposal.sections) === null || _proposal$sections8 === void 0 ? void 0 : _proposal$sections8.conclusion,\n        onChange: data => handleSectionChange(\"conclusion\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(CoverImageSection, {\n        value: (_proposal$sections9 = proposal.sections) === null || _proposal$sections9 === void 0 ? void 0 : _proposal$sections9.coverImage,\n        onChange: data => handleSectionChange(\"coverImage\", data)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-8 rounded-lg shadow-md max-w-4xl mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"proposal-preview\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections0 = proposal.sections) === null || _proposal$sections0 === void 0 ? void 0 : (_proposal$sections0$c = _proposal$sections0.companyInfo) === null || _proposal$sections0$c === void 0 ? void 0 : _proposal$sections0$c.name) || \"[Your Company's Name]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections1 = proposal.sections) === null || _proposal$sections1 === void 0 ? void 0 : (_proposal$sections1$c = _proposal$sections1.companyInfo) === null || _proposal$sections1$c === void 0 ? void 0 : _proposal$sections1$c.address) || \"[Your Company's Address]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections10 = proposal.sections) === null || _proposal$sections10 === void 0 ? void 0 : (_proposal$sections10$ = _proposal$sections10.companyInfo) === null || _proposal$sections10$ === void 0 ? void 0 : _proposal$sections10$.cityStateZip) || \"[City, State, ZIP Code]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections11 = proposal.sections) === null || _proposal$sections11 === void 0 ? void 0 : (_proposal$sections11$ = _proposal$sections11.companyInfo) === null || _proposal$sections11$ === void 0 ? void 0 : _proposal$sections11$.email) || \"[Email Address]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections12 = proposal.sections) === null || _proposal$sections12 === void 0 ? void 0 : (_proposal$sections12$ = _proposal$sections12.companyInfo) === null || _proposal$sections12$ === void 0 ? void 0 : _proposal$sections12$.phone) || \"[Phone Number]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections13 = proposal.sections) === null || _proposal$sections13 === void 0 ? void 0 : (_proposal$sections13$ = _proposal$sections13.basicInfo) === null || _proposal$sections13$ === void 0 ? void 0 : _proposal$sections13$.date) || \"[Date]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections14 = proposal.sections) === null || _proposal$sections14 === void 0 ? void 0 : (_proposal$sections14$ = _proposal$sections14.recipientInfo) === null || _proposal$sections14$ === void 0 ? void 0 : _proposal$sections14$.name) || \"[Recipient's Name]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections15 = proposal.sections) === null || _proposal$sections15 === void 0 ? void 0 : (_proposal$sections15$ = _proposal$sections15.recipientInfo) === null || _proposal$sections15$ === void 0 ? void 0 : _proposal$sections15$.company) || \"[Recipient's Company]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections16 = proposal.sections) === null || _proposal$sections16 === void 0 ? void 0 : (_proposal$sections16$ = _proposal$sections16.recipientInfo) === null || _proposal$sections16$ === void 0 ? void 0 : _proposal$sections16$.address) || \"[Recipient's Address]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections17 = proposal.sections) === null || _proposal$sections17 === void 0 ? void 0 : (_proposal$sections17$ = _proposal$sections17.recipientInfo) === null || _proposal$sections17$ === void 0 ? void 0 : _proposal$sections17$.cityStateZip) || \"[City, State, ZIP Code]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"font-bold\",\n            children: [\"Subject: Proposal for \", ((_proposal$sections18 = proposal.sections) === null || _proposal$sections18 === void 0 ? void 0 : (_proposal$sections18$ = _proposal$sections18.basicInfo) === null || _proposal$sections18$ === void 0 ? void 0 : _proposal$sections18$.serviceName) || \"[Service Name]\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Dear \", ((_proposal$sections19 = proposal.sections) === null || _proposal$sections19 === void 0 ? void 0 : (_proposal$sections19$ = _proposal$sections19.recipientInfo) === null || _proposal$sections19$ === void 0 ? void 0 : _proposal$sections19$.name) || \"[Recipient's Name]\", \",\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4\",\n            children: [\"We are pleased to submit this proposal to provide \", ((_proposal$sections20 = proposal.sections) === null || _proposal$sections20 === void 0 ? void 0 : (_proposal$sections20$ = _proposal$sections20.basicInfo) === null || _proposal$sections20$ === void 0 ? void 0 : _proposal$sections20$.serviceName) || \"[Service Name]\", \" for \", ((_proposal$sections21 = proposal.sections) === null || _proposal$sections21 === void 0 ? void 0 : (_proposal$sections21$ = _proposal$sections21.recipientInfo) === null || _proposal$sections21$ === void 0 ? void 0 : _proposal$sections21$.company) || \"[Recipient's Company]\", \". We believe that our expertise and experience make us an ideal partner to meet your needs and exceed your expectations.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-2\",\n            children: \"1. Introduction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            dangerouslySetInnerHTML: {\n              __html: ((_proposal$sections22 = proposal.sections) === null || _proposal$sections22 === void 0 ? void 0 : (_proposal$sections22$ = _proposal$sections22.projectDetails) === null || _proposal$sections22$ === void 0 ? void 0 : _proposal$sections22$.introduction) || \"Purpose: Briefly describe the purpose of the proposal.<br>Background: Provide a brief background of your company and its qualifications.<br>Objectives: Outline the objectives of the proposed service.\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-2\",\n            children: \"2. Scope of Services\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            dangerouslySetInnerHTML: {\n              __html: ((_proposal$sections23 = proposal.sections) === null || _proposal$sections23 === void 0 ? void 0 : (_proposal$sections23$ = _proposal$sections23.services) === null || _proposal$sections23$ === void 0 ? void 0 : _proposal$sections23$.description) || \"Services Offered: Detailed description of the services to be provided.<br>Deliverables: List of specific deliverables that the client can expect.<br>Exclusions: Any services or deliverables that are explicitly not included.\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), ((_proposal$sections24 = proposal.sections) === null || _proposal$sections24 === void 0 ? void 0 : (_proposal$sections24$ = _proposal$sections24.services) === null || _proposal$sections24$ === void 0 ? void 0 : _proposal$sections24$.items) && proposal.sections.services.items.length > 0 && /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full border-collapse mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"border p-2 text-left\",\n                  children: \"Item\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"border p-2 text-left\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: proposal.sections.services.items.map((service, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"border p-2\",\n                  children: service.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"border p-2\",\n                  children: service.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-2\",\n            children: \"3. Approach and Methodology\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            dangerouslySetInnerHTML: {\n              __html: ((_proposal$sections25 = proposal.sections) === null || _proposal$sections25 === void 0 ? void 0 : (_proposal$sections25$ = _proposal$sections25.projectDetails) === null || _proposal$sections25$ === void 0 ? void 0 : _proposal$sections25$.methodology) || \"Methodology: Describe the approach and methods that will be used to deliver the services.<br>Project Plan: Outline the project plan, including key milestones and timelines.\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-2\",\n            children: \"4. Timeline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), ((_proposal$sections26 = proposal.sections) === null || _proposal$sections26 === void 0 ? void 0 : _proposal$sections26.milestones) && proposal.sections.milestones.length > 0 && /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full border-collapse mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"border p-2 text-left\",\n                  children: \"Milestone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"border p-2 text-left\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"border p-2 text-left\",\n                  children: \"Deadline\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: proposal.sections.milestones.map((milestone, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"border p-2\",\n                  children: milestone.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"border p-2\",\n                  children: milestone.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"border p-2\",\n                  children: milestone.deadline\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-bold mb-2\",\n            children: \"8. Conclusion\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            dangerouslySetInnerHTML: {\n              __html: ((_proposal$sections27 = proposal.sections) === null || _proposal$sections27 === void 0 ? void 0 : (_proposal$sections27$ = _proposal$sections27.conclusion) === null || _proposal$sections27$ === void 0 ? void 0 : _proposal$sections27$.text) || \"Summary: Summarize the proposal and reiterate the benefits of the proposed services.<br>Call to Action: Encourage the client to take the next steps, such as contacting your company to discuss the proposal further.\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-4\",\n            children: [\"We look forward to the opportunity to work with \", ((_proposal$sections28 = proposal.sections) === null || _proposal$sections28 === void 0 ? void 0 : (_proposal$sections28$ = _proposal$sections28.recipientInfo) === null || _proposal$sections28$ === void 0 ? void 0 : _proposal$sections28$.company) || \"[Recipient's Company]\", \" and provide \", ((_proposal$sections29 = proposal.sections) === null || _proposal$sections29 === void 0 ? void 0 : (_proposal$sections29$ = _proposal$sections29.basicInfo) === null || _proposal$sections29$ === void 0 ? void 0 : _proposal$sections29$.serviceName) || \"[Service Name]\", \". Please feel free to contact us if you have any questions or need further information.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Sincerely,\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-6\",\n            children: ((_proposal$sections30 = proposal.sections) === null || _proposal$sections30 === void 0 ? void 0 : (_proposal$sections30$ = _proposal$sections30.companyInfo) === null || _proposal$sections30$ === void 0 ? void 0 : _proposal$sections30$.contactName) || \"[Your Name]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections31 = proposal.sections) === null || _proposal$sections31 === void 0 ? void 0 : (_proposal$sections31$ = _proposal$sections31.companyInfo) === null || _proposal$sections31$ === void 0 ? void 0 : _proposal$sections31$.contactTitle) || \"[Your Position]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: ((_proposal$sections32 = proposal.sections) === null || _proposal$sections32 === void 0 ? void 0 : (_proposal$sections32$ = _proposal$sections32.companyInfo) === null || _proposal$sections32$ === void 0 ? void 0 : _proposal$sections32$.name) || \"[Your Company's Name]\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this), showAIReviewModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6 max-w-md w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold mb-4\",\n          children: !reviewComplete ? \"AI Review\" : \"AI Suggestions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), !reviewComplete ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            children: \"Would you like our AI to review your proposal and suggest improvements?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this), isReviewing ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col items-center justify-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Analyzing your proposal...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseModal,\n              className: \"px-4 py-2 border rounded hover:bg-gray-100\",\n              children: \"Skip\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAIReview,\n              className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n              children: \"Review with AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold mb-2\",\n              children: \"Cover Image Suggestion:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 p-3 rounded mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium\",\n                children: \"Prompt:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                children: aiSuggestions.coverImage.prompt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: aiSuggestions.coverImage.rationale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold mb-2\",\n              children: \"Content Improvements:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-disc pl-5 space-y-1\",\n              children: aiSuggestions.contentImprovements.map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-sm\",\n                children: suggestion\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseModal,\n              className: \"px-4 py-2 border rounded hover:bg-gray-100\",\n              children: \"Ignore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleApplySuggestions,\n              className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n              children: \"Apply Suggestions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n}\n_s(CreateProposal, \"PKU8lMw1EOfDkKcmreHehWUqO0U=\");\n_c = CreateProposal;\nvar _c;\n$RefreshReg$(_c, \"CreateProposal\");", "map": {"version": 3, "names": ["React", "useState", "BasicInfoSection", "CompanyInfoSection", "RecipientInfoSection", "ProjectDetailsSection", "ServicesSection", "MilestonesSection", "TermsSection", "ConclusionSection", "CoverImageSection", "Proposal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreateProposal", "_s", "_proposal$sections", "_proposal$sections2", "_proposal$sections3", "_proposal$sections4", "_proposal$sections5", "_proposal$sections6", "_proposal$sections7", "_proposal$sections8", "_proposal$sections9", "_proposal$sections0", "_proposal$sections0$c", "_proposal$sections1", "_proposal$sections1$c", "_proposal$sections10", "_proposal$sections10$", "_proposal$sections11", "_proposal$sections11$", "_proposal$sections12", "_proposal$sections12$", "_proposal$sections13", "_proposal$sections13$", "_proposal$sections14", "_proposal$sections14$", "_proposal$sections15", "_proposal$sections15$", "_proposal$sections16", "_proposal$sections16$", "_proposal$sections17", "_proposal$sections17$", "_proposal$sections18", "_proposal$sections18$", "_proposal$sections19", "_proposal$sections19$", "_proposal$sections20", "_proposal$sections20$", "_proposal$sections21", "_proposal$sections21$", "_proposal$sections22", "_proposal$sections22$", "_proposal$sections23", "_proposal$sections23$", "_proposal$sections24", "_proposal$sections24$", "_proposal$sections25", "_proposal$sections25$", "_proposal$sections26", "_proposal$sections27", "_proposal$sections27$", "_proposal$sections28", "_proposal$sections28$", "_proposal$sections29", "_proposal$sections29$", "_proposal$sections30", "_proposal$sections30$", "_proposal$sections31", "_proposal$sections31$", "_proposal$sections32", "_proposal$sections32$", "proposal", "setProposal", "activeTab", "setActiveTab", "showAIReviewModal", "setShowAIReviewModal", "isReviewing", "setIsReviewing", "reviewComplete", "setReviewComplete", "aiSuggestions", "setAiSuggestions", "handleSectionChange", "sectionName", "data", "prev", "updated", "sections", "handleSave", "handleAIReview", "Promise", "resolve", "setTimeout", "mockSuggestions", "coverImage", "prompt", "rationale", "contentImprovements", "error", "console", "handleCloseModal", "handleApplySuggestions", "log", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "basicInfo", "onChange", "companyInfo", "recipientInfo", "projectDetails", "services", "milestones", "terms", "conclusion", "name", "address", "cityStateZip", "email", "phone", "date", "company", "serviceName", "dangerouslySetInnerHTML", "__html", "introduction", "description", "items", "length", "map", "service", "index", "methodology", "milestone", "title", "deadline", "text", "contactName", "contactTitle", "suggestion", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/dhobdyjr/ProposalPro/src/Pages/CreateProposal.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport BasicInfoSection from \"../Components/BasicInfoSection\";\nimport CompanyInfoSection from \"../Components/CompanyInfoSection\";\nimport RecipientInfoSection from \"../Components/RecipientInfoSection\";\nimport ProjectDetailsSection from \"../Components/ProjectDetailsSection\";\nimport ServicesSection from \"../Components/ServicesSection\";\nimport MilestonesSection from \"../Components/MilestonesSection\";\nimport TermsSection from \"../Components/TermsSection\";\nimport ConclusionSection from \"../Components/ConclusionSection\";\nimport CoverImageSection from \"../Components/CoverImageSection\";\nimport Proposal from \"../Entities/Proposal\";\n\nexport default function CreateProposal() {\n  const [proposal, setProposal] = useState(new Proposal());\n  const [activeTab, setActiveTab] = useState(\"edit\"); // \"edit\" or \"preview\"\n  const [showAIReviewModal, setShowAIReviewModal] = useState(false);\n  const [isReviewing, setIsReviewing] = useState(false);\n  const [reviewComplete, setReviewComplete] = useState(false);\n  const [aiSuggestions, setAiSuggestions] = useState(null);\n\n  const handleSectionChange = (sectionName, data) => {\n    setProposal(prev => {\n      const updated = {...prev};\n      updated.sections = updated.sections || {};\n      updated.sections[sectionName] = data;\n      return updated;\n    });\n  };\n\n  const handleSave = () => {\n    // Show AI review confirmation modal\n    setShowAIReviewModal(true);\n  };\n\n  const handleAIReview = async () => {\n    setIsReviewing(true);\n    \n    try {\n      // Simulate AI review process\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // In a real implementation, you would call your AI service here\n      // const response = await fetch('/api/ai-review', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(proposal)\n      // });\n      // const data = await response.json();\n      \n      // Mock AI suggestions\n      const mockSuggestions = {\n        coverImage: {\n          prompt: \"A professional business proposal with abstract blue and purple gradient elements, featuring a handshake silhouette symbolizing partnership\",\n          rationale: \"This image conveys professionalism and partnership, aligning with your proposal's tone and purpose.\"\n        },\n        contentImprovements: [\n          \"Consider adding more specific metrics in your milestones section to make progress more measurable.\",\n          \"The introduction could benefit from a stronger value proposition statement.\",\n          \"Your pricing structure might be more compelling with tiered options.\"\n        ]\n      };\n      \n      setAiSuggestions(mockSuggestions);\n      setReviewComplete(true);\n    } catch (error) {\n      console.error(\"AI review failed:\", error);\n    } finally {\n      setIsReviewing(false);\n    }\n  };\n\n  const handleCloseModal = () => {\n    setShowAIReviewModal(false);\n    setReviewComplete(false);\n    setAiSuggestions(null);\n  };\n\n  const handleApplySuggestions = () => {\n    // Here you would apply the AI suggestions to your proposal\n    // For now, we'll just close the modal and save\n    handleCloseModal();\n    console.log(\"Saving proposal:\", proposal);\n    alert(\"Proposal saved successfully with AI recommendations!\");\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-bold\">Create Proposal</h1>\n        <div className=\"flex gap-3\">\n          <button \n            onClick={() => setActiveTab(\"edit\")}\n            className={`px-4 py-2 rounded ${activeTab === \"edit\" ? \"bg-blue-500 text-white\" : \"bg-gray-200\"}`}\n          >\n            Edit\n          </button>\n          <button \n            onClick={() => setActiveTab(\"preview\")}\n            className={`px-4 py-2 rounded ${activeTab === \"preview\" ? \"bg-blue-500 text-white\" : \"bg-gray-200\"}`}\n          >\n            Preview\n          </button>\n          <button \n            onClick={handleSave}\n            className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\"\n          >\n            Save Proposal\n          </button>\n        </div>\n      </div>\n\n      {activeTab === \"edit\" ? (\n        <div className=\"space-y-6\">\n          <BasicInfoSection \n            value={proposal.sections?.basicInfo} \n            onChange={(data) => handleSectionChange(\"basicInfo\", data)} \n          />\n          <CompanyInfoSection \n            value={proposal.sections?.companyInfo} \n            onChange={(data) => handleSectionChange(\"companyInfo\", data)} \n          />\n          <RecipientInfoSection \n            value={proposal.sections?.recipientInfo} \n            onChange={(data) => handleSectionChange(\"recipientInfo\", data)} \n          />\n          <ProjectDetailsSection \n            value={proposal.sections?.projectDetails} \n            onChange={(data) => handleSectionChange(\"projectDetails\", data)} \n          />\n          <ServicesSection \n            value={proposal.sections?.services} \n            onChange={(data) => handleSectionChange(\"services\", data)} \n          />\n          <MilestonesSection \n            value={proposal.sections?.milestones} \n            onChange={(data) => handleSectionChange(\"milestones\", data)} \n          />\n          <TermsSection \n            value={proposal.sections?.terms} \n            onChange={(data) => handleSectionChange(\"terms\", data)} \n          />\n          <ConclusionSection \n            value={proposal.sections?.conclusion} \n            onChange={(data) => handleSectionChange(\"conclusion\", data)} \n          />\n          <CoverImageSection \n            value={proposal.sections?.coverImage} \n            onChange={(data) => handleSectionChange(\"coverImage\", data)} \n          />\n        </div>\n      ) : (\n        <div className=\"bg-white p-8 rounded-lg shadow-md max-w-4xl mx-auto\">\n          {/* Proposal Preview - formatted according to your template */}\n          <div className=\"proposal-preview\">\n            {/* Company Header */}\n            <div className=\"mb-8\">\n              <p>{proposal.sections?.companyInfo?.name || \"[Your Company's Name]\"}</p>\n              <p>{proposal.sections?.companyInfo?.address || \"[Your Company's Address]\"}</p>\n              <p>{proposal.sections?.companyInfo?.cityStateZip || \"[City, State, ZIP Code]\"}</p>\n              <p>{proposal.sections?.companyInfo?.email || \"[Email Address]\"}</p>\n              <p>{proposal.sections?.companyInfo?.phone || \"[Phone Number]\"}</p>\n              <p>{proposal.sections?.basicInfo?.date || \"[Date]\"}</p>\n            </div>\n            \n            {/* Recipient Info */}\n            <div className=\"mb-8\">\n              <p>{proposal.sections?.recipientInfo?.name || \"[Recipient's Name]\"}</p>\n              <p>{proposal.sections?.recipientInfo?.company || \"[Recipient's Company]\"}</p>\n              <p>{proposal.sections?.recipientInfo?.address || \"[Recipient's Address]\"}</p>\n              <p>{proposal.sections?.recipientInfo?.cityStateZip || \"[City, State, ZIP Code]\"}</p>\n            </div>\n            \n            {/* Subject Line */}\n            <div className=\"mb-8\">\n              <p className=\"font-bold\">Subject: Proposal for {proposal.sections?.basicInfo?.serviceName || \"[Service Name]\"}</p>\n            </div>\n            \n            {/* Greeting */}\n            <div className=\"mb-8\">\n              <p>Dear {proposal.sections?.recipientInfo?.name || \"[Recipient's Name]\"},</p>\n              <p className=\"mt-4\">\n                We are pleased to submit this proposal to provide {proposal.sections?.basicInfo?.serviceName || \"[Service Name]\"} for {proposal.sections?.recipientInfo?.company || \"[Recipient's Company]\"}. \n                We believe that our expertise and experience make us an ideal partner to meet your needs and exceed your expectations.\n              </p>\n            </div>\n            \n            {/* Main Content Sections */}\n            <div className=\"mb-8\">\n              <h2 className=\"text-xl font-bold mb-2\">1. Introduction</h2>\n              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.projectDetails?.introduction || \"Purpose: Briefly describe the purpose of the proposal.<br>Background: Provide a brief background of your company and its qualifications.<br>Objectives: Outline the objectives of the proposed service.\" }} />\n            </div>\n            \n            <div className=\"mb-8\">\n              <h2 className=\"text-xl font-bold mb-2\">2. Scope of Services</h2>\n              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.services?.description || \"Services Offered: Detailed description of the services to be provided.<br>Deliverables: List of specific deliverables that the client can expect.<br>Exclusions: Any services or deliverables that are explicitly not included.\" }} />\n              \n              {proposal.sections?.services?.items && proposal.sections.services.items.length > 0 && (\n                <table className=\"w-full border-collapse mt-4\">\n                  <thead>\n                    <tr>\n                      <th className=\"border p-2 text-left\">Item</th>\n                      <th className=\"border p-2 text-left\">Description</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {proposal.sections.services.items.map((service, index) => (\n                      <tr key={index}>\n                        <td className=\"border p-2\">{service.name}</td>\n                        <td className=\"border p-2\">{service.description}</td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              )}\n            </div>\n            \n            <div className=\"mb-8\">\n              <h2 className=\"text-xl font-bold mb-2\">3. Approach and Methodology</h2>\n              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.projectDetails?.methodology || \"Methodology: Describe the approach and methods that will be used to deliver the services.<br>Project Plan: Outline the project plan, including key milestones and timelines.\" }} />\n            </div>\n            \n            <div className=\"mb-8\">\n              <h2 className=\"text-xl font-bold mb-2\">4. Timeline</h2>\n              {proposal.sections?.milestones && proposal.sections.milestones.length > 0 && (\n                <table className=\"w-full border-collapse mt-4\">\n                  <thead>\n                    <tr>\n                      <th className=\"border p-2 text-left\">Milestone</th>\n                      <th className=\"border p-2 text-left\">Description</th>\n                      <th className=\"border p-2 text-left\">Deadline</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {proposal.sections.milestones.map((milestone, index) => (\n                      <tr key={index}>\n                        <td className=\"border p-2\">{milestone.title}</td>\n                        <td className=\"border p-2\">{milestone.description}</td>\n                        <td className=\"border p-2\">{milestone.deadline}</td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              )}\n            </div>\n            \n            {/* Additional sections would continue here */}\n            \n            <div className=\"mb-8\">\n              <h2 className=\"text-xl font-bold mb-2\">8. Conclusion</h2>\n              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.conclusion?.text || \"Summary: Summarize the proposal and reiterate the benefits of the proposed services.<br>Call to Action: Encourage the client to take the next steps, such as contacting your company to discuss the proposal further.\" }} />\n              <p className=\"mt-4\">\n                We look forward to the opportunity to work with {proposal.sections?.recipientInfo?.company || \"[Recipient's Company]\"} and provide {proposal.sections?.basicInfo?.serviceName || \"[Service Name]\"}. \n                Please feel free to contact us if you have any questions or need further information.\n              </p>\n            </div>\n            \n            {/* Signature */}\n            <div className=\"mt-12\">\n              <p>Sincerely,</p>\n              <p className=\"mt-6\">{proposal.sections?.companyInfo?.contactName || \"[Your Name]\"}</p>\n              <p>{proposal.sections?.companyInfo?.contactTitle || \"[Your Position]\"}</p>\n              <p>{proposal.sections?.companyInfo?.name || \"[Your Company's Name]\"}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* AI Review Modal */}\n      {showAIReviewModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-md w-full\">\n            <h2 className=\"text-xl font-bold mb-4\">\n              {!reviewComplete ? \"AI Review\" : \"AI Suggestions\"}\n            </h2>\n            \n            {!reviewComplete ? (\n              <>\n                <p className=\"mb-4\">\n                  Would you like our AI to review your proposal and suggest improvements?\n                </p>\n                \n                {isReviewing ? (\n                  <div className=\"flex flex-col items-center justify-center py-4\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4\"></div>\n                    <p>Analyzing your proposal...</p>\n                  </div>\n                ) : (\n                  <div className=\"flex justify-end gap-3\">\n                    <button\n                      onClick={handleCloseModal}\n                      className=\"px-4 py-2 border rounded hover:bg-gray-100\"\n                    >\n                      Skip\n                    </button>\n                    <button\n                      onClick={handleAIReview}\n                      className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n                    >\n                      Review with AI\n                    </button>\n                  </div>\n                )}\n              </>\n            ) : (\n              <>\n                <div className=\"mb-6\">\n                  <h3 className=\"font-semibold mb-2\">Cover Image Suggestion:</h3>\n                  <div className=\"bg-gray-50 p-3 rounded mb-2\">\n                    <p className=\"text-sm font-medium\">Prompt:</p>\n                    <p className=\"text-sm\">{aiSuggestions.coverImage.prompt}</p>\n                  </div>\n                  <p className=\"text-sm text-gray-600\">{aiSuggestions.coverImage.rationale}</p>\n                </div>\n                \n                <div className=\"mb-6\">\n                  <h3 className=\"font-semibold mb-2\">Content Improvements:</h3>\n                  <ul className=\"list-disc pl-5 space-y-1\">\n                    {aiSuggestions.contentImprovements.map((suggestion, index) => (\n                      <li key={index} className=\"text-sm\">{suggestion}</li>\n                    ))}\n                  </ul>\n                </div>\n                \n                <div className=\"flex justify-end gap-3\">\n                  <button\n                    onClick={handleCloseModal}\n                    className=\"px-4 py-2 border rounded hover:bg-gray-100\"\n                  >\n                    Ignore\n                  </button>\n                  <button\n                    onClick={handleApplySuggestions}\n                    className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\"\n                  >\n                    Apply Suggestions\n                  </button>\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,OAAOC,qBAAqB,MAAM,qCAAqC;AACvE,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,QAAQ,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;EACvC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,IAAIU,QAAQ,CAAC,CAAC,CAAC;EACxD,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAG9E,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC+E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqF,aAAa,EAAEC,gBAAgB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMuF,mBAAmB,GAAGA,CAACC,WAAW,EAAEC,IAAI,KAAK;IACjDb,WAAW,CAACc,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG;QAAC,GAAGD;MAAI,CAAC;MACzBC,OAAO,CAACC,QAAQ,GAAGD,OAAO,CAACC,QAAQ,IAAI,CAAC,CAAC;MACzCD,OAAO,CAACC,QAAQ,CAACJ,WAAW,CAAC,GAAGC,IAAI;MACpC,OAAOE,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB;IACAb,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMc,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCZ,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAM,IAAIa,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAME,eAAe,GAAG;QACtBC,UAAU,EAAE;UACVC,MAAM,EAAE,4IAA4I;UACpJC,SAAS,EAAE;QACb,CAAC;QACDC,mBAAmB,EAAE,CACnB,oGAAoG,EACpG,6EAA6E,EAC7E,sEAAsE;MAE1E,CAAC;MAEDhB,gBAAgB,CAACY,eAAe,CAAC;MACjCd,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C,CAAC,SAAS;MACRrB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzB,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,iBAAiB,CAAC,KAAK,CAAC;IACxBE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMoB,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA;IACAD,gBAAgB,CAAC,CAAC;IAClBD,OAAO,CAACG,GAAG,CAAC,kBAAkB,EAAEhC,QAAQ,CAAC;IACzCiC,KAAK,CAAC,sDAAsD,CAAC;EAC/D,CAAC;EAED,oBACEhG,OAAA;IAAKiG,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBlG,OAAA;MAAKiG,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDlG,OAAA;QAAIiG,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDtG,OAAA;QAAKiG,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBlG,OAAA;UACEuG,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAAC,MAAM,CAAE;UACpC+B,SAAS,EAAE,qBAAqBhC,SAAS,KAAK,MAAM,GAAG,wBAAwB,GAAG,aAAa,EAAG;UAAAiC,QAAA,EACnG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA;UACEuG,OAAO,EAAEA,CAAA,KAAMrC,YAAY,CAAC,SAAS,CAAE;UACvC+B,SAAS,EAAE,qBAAqBhC,SAAS,KAAK,SAAS,GAAG,wBAAwB,GAAG,aAAa,EAAG;UAAAiC,QAAA,EACtG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA;UACEuG,OAAO,EAAEtB,UAAW;UACpBgB,SAAS,EAAC,8DAA8D;UAAAC,QAAA,EACzE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELrC,SAAS,KAAK,MAAM,gBACnBjE,OAAA;MAAKiG,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBlG,OAAA,CAACX,gBAAgB;QACfmH,KAAK,GAAAnG,kBAAA,GAAE0D,QAAQ,CAACiB,QAAQ,cAAA3E,kBAAA,uBAAjBA,kBAAA,CAAmBoG,SAAU;QACpCC,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,WAAW,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACFtG,OAAA,CAACV,kBAAkB;QACjBkH,KAAK,GAAAlG,mBAAA,GAAEyD,QAAQ,CAACiB,QAAQ,cAAA1E,mBAAA,uBAAjBA,mBAAA,CAAmBqG,WAAY;QACtCD,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,aAAa,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACFtG,OAAA,CAACT,oBAAoB;QACnBiH,KAAK,GAAAjG,mBAAA,GAAEwD,QAAQ,CAACiB,QAAQ,cAAAzE,mBAAA,uBAAjBA,mBAAA,CAAmBqG,aAAc;QACxCF,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,eAAe,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACFtG,OAAA,CAACR,qBAAqB;QACpBgH,KAAK,GAAAhG,mBAAA,GAAEuD,QAAQ,CAACiB,QAAQ,cAAAxE,mBAAA,uBAAjBA,mBAAA,CAAmBqG,cAAe;QACzCH,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,gBAAgB,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACFtG,OAAA,CAACP,eAAe;QACd+G,KAAK,GAAA/F,mBAAA,GAAEsD,QAAQ,CAACiB,QAAQ,cAAAvE,mBAAA,uBAAjBA,mBAAA,CAAmBqG,QAAS;QACnCJ,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,UAAU,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACFtG,OAAA,CAACN,iBAAiB;QAChB8G,KAAK,GAAA9F,mBAAA,GAAEqD,QAAQ,CAACiB,QAAQ,cAAAtE,mBAAA,uBAAjBA,mBAAA,CAAmBqG,UAAW;QACrCL,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,YAAY,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACFtG,OAAA,CAACL,YAAY;QACX6G,KAAK,GAAA7F,mBAAA,GAAEoD,QAAQ,CAACiB,QAAQ,cAAArE,mBAAA,uBAAjBA,mBAAA,CAAmBqG,KAAM;QAChCN,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,OAAO,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACFtG,OAAA,CAACJ,iBAAiB;QAChB4G,KAAK,GAAA5F,mBAAA,GAAEmD,QAAQ,CAACiB,QAAQ,cAAApE,mBAAA,uBAAjBA,mBAAA,CAAmBqG,UAAW;QACrCP,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,YAAY,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACFtG,OAAA,CAACH,iBAAiB;QAChB2G,KAAK,GAAA3F,mBAAA,GAAEkD,QAAQ,CAACiB,QAAQ,cAAAnE,mBAAA,uBAAjBA,mBAAA,CAAmB0E,UAAW;QACrCmB,QAAQ,EAAG7B,IAAI,IAAKF,mBAAmB,CAAC,YAAY,EAAEE,IAAI;MAAE;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENtG,OAAA;MAAKiG,SAAS,EAAC,qDAAqD;MAAAC,QAAA,eAElElG,OAAA;QAAKiG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE/BlG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAAkG,QAAA,EAAI,EAAApF,mBAAA,GAAAiD,QAAQ,CAACiB,QAAQ,cAAAlE,mBAAA,wBAAAC,qBAAA,GAAjBD,mBAAA,CAAmB6F,WAAW,cAAA5F,qBAAA,uBAA9BA,qBAAA,CAAgCmG,IAAI,KAAI;UAAuB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEtG,OAAA;YAAAkG,QAAA,EAAI,EAAAlF,mBAAA,GAAA+C,QAAQ,CAACiB,QAAQ,cAAAhE,mBAAA,wBAAAC,qBAAA,GAAjBD,mBAAA,CAAmB2F,WAAW,cAAA1F,qBAAA,uBAA9BA,qBAAA,CAAgCkG,OAAO,KAAI;UAA0B;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EtG,OAAA;YAAAkG,QAAA,EAAI,EAAAhF,oBAAA,GAAA6C,QAAQ,CAACiB,QAAQ,cAAA9D,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmByF,WAAW,cAAAxF,qBAAA,uBAA9BA,qBAAA,CAAgCiG,YAAY,KAAI;UAAyB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFtG,OAAA;YAAAkG,QAAA,EAAI,EAAA9E,oBAAA,GAAA2C,QAAQ,CAACiB,QAAQ,cAAA5D,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBuF,WAAW,cAAAtF,qBAAA,uBAA9BA,qBAAA,CAAgCgG,KAAK,KAAI;UAAiB;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEtG,OAAA;YAAAkG,QAAA,EAAI,EAAA5E,oBAAA,GAAAyC,QAAQ,CAACiB,QAAQ,cAAA1D,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBqF,WAAW,cAAApF,qBAAA,uBAA9BA,qBAAA,CAAgC+F,KAAK,KAAI;UAAgB;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEtG,OAAA;YAAAkG,QAAA,EAAI,EAAA1E,oBAAA,GAAAuC,QAAQ,CAACiB,QAAQ,cAAAxD,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBiF,SAAS,cAAAhF,qBAAA,uBAA5BA,qBAAA,CAA8B8F,IAAI,KAAI;UAAQ;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eAGNtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAAkG,QAAA,EAAI,EAAAxE,oBAAA,GAAAqC,QAAQ,CAACiB,QAAQ,cAAAtD,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBkF,aAAa,cAAAjF,qBAAA,uBAAhCA,qBAAA,CAAkCuF,IAAI,KAAI;UAAoB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEtG,OAAA;YAAAkG,QAAA,EAAI,EAAAtE,oBAAA,GAAAmC,QAAQ,CAACiB,QAAQ,cAAApD,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBgF,aAAa,cAAA/E,qBAAA,uBAAhCA,qBAAA,CAAkC2F,OAAO,KAAI;UAAuB;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EtG,OAAA;YAAAkG,QAAA,EAAI,EAAApE,oBAAA,GAAAiC,QAAQ,CAACiB,QAAQ,cAAAlD,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmB8E,aAAa,cAAA7E,qBAAA,uBAAhCA,qBAAA,CAAkCoF,OAAO,KAAI;UAAuB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EtG,OAAA;YAAAkG,QAAA,EAAI,EAAAlE,oBAAA,GAAA+B,QAAQ,CAACiB,QAAQ,cAAAhD,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmB4E,aAAa,cAAA3E,qBAAA,uBAAhCA,qBAAA,CAAkCmF,YAAY,KAAI;UAAyB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,eAGNtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBlG,OAAA;YAAGiG,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,wBAAsB,EAAC,EAAAhE,oBAAA,GAAA6B,QAAQ,CAACiB,QAAQ,cAAA9C,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBuE,SAAS,cAAAtE,qBAAA,uBAA5BA,qBAAA,CAA8BsF,WAAW,KAAI,gBAAgB;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC,eAGNtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAAkG,QAAA,GAAG,OAAK,EAAC,EAAA9D,oBAAA,GAAA2B,QAAQ,CAACiB,QAAQ,cAAA5C,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBwE,aAAa,cAAAvE,qBAAA,uBAAhCA,qBAAA,CAAkC6E,IAAI,KAAI,oBAAoB,EAAC,GAAC;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7EtG,OAAA;YAAGiG,SAAS,EAAC,MAAM;YAAAC,QAAA,GAAC,oDACgC,EAAC,EAAA5D,oBAAA,GAAAyB,QAAQ,CAACiB,QAAQ,cAAA1C,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBmE,SAAS,cAAAlE,qBAAA,uBAA5BA,qBAAA,CAA8BkF,WAAW,KAAI,gBAAgB,EAAC,OAAK,EAAC,EAAAjF,oBAAA,GAAAuB,QAAQ,CAACiB,QAAQ,cAAAxC,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBoE,aAAa,cAAAnE,qBAAA,uBAAhCA,qBAAA,CAAkC+E,OAAO,KAAI,uBAAuB,EAAC,0HAE9L;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAIiG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DtG,OAAA;YAAK0H,uBAAuB,EAAE;cAAEC,MAAM,EAAE,EAAAjF,oBAAA,GAAAqB,QAAQ,CAACiB,QAAQ,cAAAtC,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBmE,cAAc,cAAAlE,qBAAA,uBAAjCA,qBAAA,CAAmCiF,YAAY,KAAI;YAA0M;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvS,CAAC,eAENtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAIiG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEtG,OAAA;YAAK0H,uBAAuB,EAAE;cAAEC,MAAM,EAAE,EAAA/E,oBAAA,GAAAmB,QAAQ,CAACiB,QAAQ,cAAApC,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBkE,QAAQ,cAAAjE,qBAAA,uBAA3BA,qBAAA,CAA6BgF,WAAW,KAAI;YAAkO;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAE1T,EAAAxD,oBAAA,GAAAiB,QAAQ,CAACiB,QAAQ,cAAAlC,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBgE,QAAQ,cAAA/D,qBAAA,uBAA3BA,qBAAA,CAA6B+E,KAAK,KAAI/D,QAAQ,CAACiB,QAAQ,CAAC8B,QAAQ,CAACgB,KAAK,CAACC,MAAM,GAAG,CAAC,iBAChF/H,OAAA;YAAOiG,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC5ClG,OAAA;cAAAkG,QAAA,eACElG,OAAA;gBAAAkG,QAAA,gBACElG,OAAA;kBAAIiG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9CtG,OAAA;kBAAIiG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRtG,OAAA;cAAAkG,QAAA,EACGnC,QAAQ,CAACiB,QAAQ,CAAC8B,QAAQ,CAACgB,KAAK,CAACE,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnDlI,OAAA;gBAAAkG,QAAA,gBACElG,OAAA;kBAAIiG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE+B,OAAO,CAACf;gBAAI;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CtG,OAAA;kBAAIiG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE+B,OAAO,CAACJ;gBAAW;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAF9C4B,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAIiG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEtG,OAAA;YAAK0H,uBAAuB,EAAE;cAAEC,MAAM,EAAE,EAAA3E,oBAAA,GAAAe,QAAQ,CAACiB,QAAQ,cAAAhC,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmB6D,cAAc,cAAA5D,qBAAA,uBAAjCA,qBAAA,CAAmCkF,WAAW,KAAI;YAA+K;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3Q,CAAC,eAENtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAIiG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtD,EAAApD,oBAAA,GAAAa,QAAQ,CAACiB,QAAQ,cAAA9B,oBAAA,uBAAjBA,oBAAA,CAAmB6D,UAAU,KAAIhD,QAAQ,CAACiB,QAAQ,CAAC+B,UAAU,CAACgB,MAAM,GAAG,CAAC,iBACvE/H,OAAA;YAAOiG,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC5ClG,OAAA;cAAAkG,QAAA,eACElG,OAAA;gBAAAkG,QAAA,gBACElG,OAAA;kBAAIiG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnDtG,OAAA;kBAAIiG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrDtG,OAAA;kBAAIiG,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRtG,OAAA;cAAAkG,QAAA,EACGnC,QAAQ,CAACiB,QAAQ,CAAC+B,UAAU,CAACiB,GAAG,CAAC,CAACI,SAAS,EAAEF,KAAK,kBACjDlI,OAAA;gBAAAkG,QAAA,gBACElG,OAAA;kBAAIiG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEkC,SAAS,CAACC;gBAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDtG,OAAA;kBAAIiG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEkC,SAAS,CAACP;gBAAW;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvDtG,OAAA;kBAAIiG,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEkC,SAAS,CAACE;gBAAQ;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA,GAH7C4B,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAINtG,OAAA;UAAKiG,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlG,OAAA;YAAIiG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDtG,OAAA;YAAK0H,uBAAuB,EAAE;cAAEC,MAAM,EAAE,EAAAxE,oBAAA,GAAAY,QAAQ,CAACiB,QAAQ,cAAA7B,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmB8D,UAAU,cAAA7D,qBAAA,uBAA7BA,qBAAA,CAA+BmF,IAAI,KAAI;YAAwN;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5StG,OAAA;YAAGiG,SAAS,EAAC,MAAM;YAAAC,QAAA,GAAC,kDAC8B,EAAC,EAAA7C,oBAAA,GAAAU,QAAQ,CAACiB,QAAQ,cAAA3B,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBuD,aAAa,cAAAtD,qBAAA,uBAAhCA,qBAAA,CAAkCkE,OAAO,KAAI,uBAAuB,EAAC,eAAa,EAAC,EAAAjE,oBAAA,GAAAQ,QAAQ,CAACiB,QAAQ,cAAAzB,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBkD,SAAS,cAAAjD,qBAAA,uBAA5BA,qBAAA,CAA8BiE,WAAW,KAAI,gBAAgB,EAAC,yFAEpM;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNtG,OAAA;UAAKiG,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBlG,OAAA;YAAAkG,QAAA,EAAG;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjBtG,OAAA;YAAGiG,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAE,EAAAzC,oBAAA,GAAAM,QAAQ,CAACiB,QAAQ,cAAAvB,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBkD,WAAW,cAAAjD,qBAAA,uBAA9BA,qBAAA,CAAgC8E,WAAW,KAAI;UAAa;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtFtG,OAAA;YAAAkG,QAAA,EAAI,EAAAvC,oBAAA,GAAAI,QAAQ,CAACiB,QAAQ,cAAArB,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmBgD,WAAW,cAAA/C,qBAAA,uBAA9BA,qBAAA,CAAgC6E,YAAY,KAAI;UAAiB;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EtG,OAAA;YAAAkG,QAAA,EAAI,EAAArC,oBAAA,GAAAE,QAAQ,CAACiB,QAAQ,cAAAnB,oBAAA,wBAAAC,qBAAA,GAAjBD,oBAAA,CAAmB8C,WAAW,cAAA7C,qBAAA,uBAA9BA,qBAAA,CAAgCoD,IAAI,KAAI;UAAuB;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnC,iBAAiB,iBAChBnE,OAAA;MAAKiG,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFlG,OAAA;QAAKiG,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDlG,OAAA;UAAIiG,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EACnC,CAAC3B,cAAc,GAAG,WAAW,GAAG;QAAgB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,EAEJ,CAAC/B,cAAc,gBACdvE,OAAA,CAAAE,SAAA;UAAAgG,QAAA,gBACElG,OAAA;YAAGiG,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAEpB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAEHjC,WAAW,gBACVrE,OAAA;YAAKiG,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DlG,OAAA;cAAKiG,SAAS,EAAC;YAAqE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3FtG,OAAA;cAAAkG,QAAA,EAAG;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,gBAENtG,OAAA;YAAKiG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrClG,OAAA;cACEuG,OAAO,EAAEV,gBAAiB;cAC1BI,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA;cACEuG,OAAO,EAAErB,cAAe;cACxBe,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EACvE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA,eACD,CAAC,gBAEHtG,OAAA,CAAAE,SAAA;UAAAgG,QAAA,gBACElG,OAAA;YAAKiG,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlG,OAAA;cAAIiG,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DtG,OAAA;cAAKiG,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClG,OAAA;gBAAGiG,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9CtG,OAAA;gBAAGiG,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAEzB,aAAa,CAACc,UAAU,CAACC;cAAM;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNtG,OAAA;cAAGiG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEzB,aAAa,CAACc,UAAU,CAACE;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eAENtG,OAAA;YAAKiG,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlG,OAAA;cAAIiG,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7DtG,OAAA;cAAIiG,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACrCzB,aAAa,CAACiB,mBAAmB,CAACsC,GAAG,CAAC,CAACU,UAAU,EAAER,KAAK,kBACvDlI,OAAA;gBAAgBiG,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAEwC;cAAU,GAAtCR,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAsC,CACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENtG,OAAA;YAAKiG,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrClG,OAAA;cACEuG,OAAO,EAAEV,gBAAiB;cAC1BI,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACvD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA;cACEuG,OAAO,EAAET,sBAAuB;cAChCG,SAAS,EAAC,8DAA8D;cAAAC,QAAA,EACzE;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAClG,EAAA,CA5UuBD,cAAc;AAAAwI,EAAA,GAAdxI,cAAc;AAAA,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}