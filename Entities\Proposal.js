// Proposal entity model
export default class Proposal {
  constructor(data = {}) {
    this.id = data.id || null;
    this.title = data.title || '';
    this.client = data.client || '';
    this.createdAt = data.createdAt || new Date();
    this.sections = data.sections || [];
    this.status = data.status || 'draft';
  }
  
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      client: this.client,
      createdAt: this.createdAt,
      sections: this.sections,
      status: this.status
    };
  }
}