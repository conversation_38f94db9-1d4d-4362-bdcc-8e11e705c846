{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Wand2 = createLucideIcon(\"Wand2\", [[\"path\", {\n  d: \"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z\",\n  key: \"1bcowg\"\n}], [\"path\", {\n  d: \"m14 7 3 3\",\n  key: \"1r5n42\"\n}], [\"path\", {\n  d: \"M5 6v4\",\n  key: \"ilb8ba\"\n}], [\"path\", {\n  d: \"M19 14v4\",\n  key: \"blhpug\"\n}], [\"path\", {\n  d: \"M10 2v2\",\n  key: \"7u0qdc\"\n}], [\"path\", {\n  d: \"M7 8H3\",\n  key: \"zfb6yr\"\n}], [\"path\", {\n  d: \"M21 16h-4\",\n  key: \"1cnmox\"\n}], [\"path\", {\n  d: \"M11 3H9\",\n  key: \"1obp7u\"\n}]]);\nexport { Wand2 as default };", "map": {"version": 3, "names": ["Wand2", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Documents\\dhobdyjr\\ProposalPro\\node_modules\\lucide-react\\src\\icons\\wand-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Wand2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNjQgMy42NC0xLjI4LTEuMjhhMS4yMSAxLjIxIDAgMCAwLTEuNzIgMEwyLjM2IDE4LjY0YTEuMjEgMS4yMSAwIDAgMCAwIDEuNzJsMS4yOCAxLjI4YTEuMiAxLjIgMCAwIDAgMS43MiAwTDIxLjY0IDUuMzZhMS4yIDEuMiAwIDAgMCAwLTEuNzJaIiAvPgogIDxwYXRoIGQ9Im0xNCA3IDMgMyIgLz4KICA8cGF0aCBkPSJNNSA2djQiIC8+CiAgPHBhdGggZD0iTTE5IDE0djQiIC8+CiAgPHBhdGggZD0iTTEwIDJ2MiIgLz4KICA8cGF0aCBkPSJNNyA4SDMiIC8+CiAgPHBhdGggZD0iTTIxIDE2aC00IiAvPgogIDxwYXRoIGQ9Ik0xMSAzSDkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/wand-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wand2 = createLucideIcon('Wand2', [\n  [\n    'path',\n    {\n      d: 'm21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z',\n      key: '1bcowg',\n    },\n  ],\n  ['path', { d: 'm14 7 3 3', key: '1r5n42' }],\n  ['path', { d: 'M5 6v4', key: 'ilb8ba' }],\n  ['path', { d: 'M19 14v4', key: 'blhpug' }],\n  ['path', { d: 'M10 2v2', key: '7u0qdc' }],\n  ['path', { d: 'M7 8H3', key: 'zfb6yr' }],\n  ['path', { d: 'M21 16h-4', key: '1cnmox' }],\n  ['path', { d: 'M11 3H9', key: '1obp7u' }],\n]);\n\nexport default Wand2;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}