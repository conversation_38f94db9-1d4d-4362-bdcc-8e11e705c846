{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\dhobdyjr\\\\ProposalPro\\\\src\\\\Pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport Proposal from \"../Entities/Proposal\";\nimport { Button } from \"../components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"../components/ui/card\";\nimport { Badge } from \"../components/ui/badge\";\nimport { Link } from \"react-router-dom\";\nimport { createPageUrl, formatDate } from \"../utils\";\nimport { Plus, FileText, Calendar, DollarSign, TrendingUp, Eye, Edit, Send, CheckCircle, XCircle, Clock } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Dashboard() {\n  _s();\n  const [proposals, setProposals] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    loadProposals();\n  }, []);\n  const loadProposals = async () => {\n    try {\n      const data = await Proposal.list(\"-created_date\");\n      setProposals(data);\n    } catch (error) {\n      console.error(\"Error loading proposals:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'draft':\n        return /*#__PURE__*/_jsxDEV(Clock, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 28\n        }, this);\n      case 'sent':\n        return /*#__PURE__*/_jsxDEV(Send, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 27\n        }, this);\n      case 'accepted':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 31\n        }, this);\n      case 'rejected':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 31\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'draft':\n        return 'bg-slate-100 text-slate-700 border-slate-200';\n      case 'sent':\n        return 'bg-blue-100 text-blue-700 border-blue-200';\n      case 'accepted':\n        return 'bg-green-100 text-green-700 border-green-200';\n      case 'rejected':\n        return 'bg-red-100 text-red-700 border-red-200';\n      default:\n        return 'bg-slate-100 text-slate-700 border-slate-200';\n    }\n  };\n  const totalValue = proposals.reduce((sum, proposal) => sum + (proposal.total_cost || 0), 0);\n  const acceptedValue = proposals.filter(p => p.status === 'accepted').reduce((sum, proposal) => sum + (proposal.total_cost || 0), 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 md:p-8 space-y-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row justify-between items-start md:items-center gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold text-slate-800 mb-2\",\n            children: \"Proposal Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-slate-600\",\n            children: \"Manage and track your professional proposals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: createPageUrl(\"CreateProposal\"),\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            className: \"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), \"Create New Proposal\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          className: \"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n            children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"text-sm font-medium text-slate-600\",\n              children: \"Total Proposals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FileText, {\n              className: \"h-4 w-4 text-indigo-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-slate-800\",\n              children: proposals.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n            children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"text-sm font-medium text-slate-600\",\n              children: \"Total Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(DollarSign, {\n              className: \"h-4 w-4 text-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-slate-800\",\n              children: [\"$\", totalValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n            children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"text-sm font-medium text-slate-600\",\n              children: \"Accepted Value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TrendingUp, {\n              className: \"h-4 w-4 text-purple-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-slate-800\",\n              children: [\"$\", acceptedValue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n            children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"text-sm font-medium text-slate-600\",\n              children: \"Success Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"h-4 w-4 text-emerald-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-slate-800\",\n              children: [proposals.length > 0 ? Math.round(proposals.filter(p => p.status === 'accepted').length / proposals.length * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"glass-effect border-white/20\",\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          children: /*#__PURE__*/_jsxDEV(CardTitle, {\n            className: \"text-xl font-semibold text-slate-800\",\n            children: \"Recent Proposals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-pulse\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-24 bg-slate-200 rounded-lg\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 21\n              }, this)\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this) : proposals.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-12 h-12 text-slate-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-slate-600 mb-2\",\n              children: \"No proposals yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-slate-500 mb-6\",\n              children: \"Create your first professional proposal to get started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: createPageUrl(\"CreateProposal\"),\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                className: \"bg-gradient-to-r from-indigo-500 to-purple-600 text-white\",\n                children: [/*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"w-4 h-4 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this), \"Create First Proposal\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n            children: proposals.map((proposal, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/60 rounded-xl border border-white/20 hover:shadow-lg transition-all duration-300 overflow-hidden\",\n              children: [proposal.cover_image_url && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-40 bg-slate-200\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: proposal.cover_image_url,\n                  alt: \"Proposal Cover\",\n                  className: \"w-full h-full object-cover\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between gap-3 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-slate-800 text-lg truncate\",\n                    title: proposal.title,\n                    children: proposal.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    className: `${getStatusColor(proposal.status)} border`,\n                    children: [getStatusIcon(proposal.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-1 capitalize\",\n                      children: proposal.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-slate-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 27\n                    }, this), formatDate(proposal.created_date)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"truncate\",\n                    children: [\"Client: \", proposal.recipient_company || proposal.recipient_name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 25\n                  }, this), proposal.total_cost && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center gap-1 font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 29\n                    }, this), proposal.total_cost.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 mt-4 pt-4 border-t border-white/20\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline\",\n                    size: \"sm\",\n                    className: \"hover:bg-white/80\",\n                    children: [/*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"w-4 h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 27\n                    }, this), \"View\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline\",\n                    size: \"sm\",\n                    className: \"hover:bg-white/80\",\n                    children: [/*#__PURE__*/_jsxDEV(Edit, {\n                      className: \"w-4 h-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 27\n                    }, this), \"Edit\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this)]\n            }, proposal.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}\n_s(Dashboard, \"+tKnlVsKG+euLVY+Yq/a79/LPH0=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Proposal", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Badge", "Link", "createPageUrl", "formatDate", "Plus", "FileText", "Calendar", "DollarSign", "TrendingUp", "Eye", "Edit", "Send", "CheckCircle", "XCircle", "Clock", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "proposals", "setProposals", "isLoading", "setIsLoading", "loadProposals", "data", "list", "error", "console", "getStatusIcon", "status", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "totalValue", "reduce", "sum", "proposal", "total_cost", "acceptedValue", "filter", "p", "children", "to", "length", "toLocaleString", "Math", "round", "Array", "map", "_", "i", "index", "cover_image_url", "src", "alt", "title", "created_date", "recipient_company", "recipient_name", "variant", "size", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/dhobdyjr/ProposalPro/src/Pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport Proposal from \"../Entities/Proposal\";\nimport { Button } from \"../components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"../components/ui/card\";\nimport { Badge } from \"../components/ui/badge\";\nimport { Link } from \"react-router-dom\";\nimport { createPageUrl, formatDate } from \"../utils\";\nimport {\n  Plus,\n  FileText,\n  Calendar,\n  DollarSign,\n  TrendingUp,\n  Eye,\n  Edit,\n  Send,\n  CheckCircle,\n  XCircle,\n  Clock\n} from \"lucide-react\";\n\nexport default function Dashboard() {\n  const [proposals, setProposals] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    loadProposals();\n  }, []);\n\n  const loadProposals = async () => {\n    try {\n      const data = await Proposal.list(\"-created_date\");\n      setProposals(data);\n    } catch (error) {\n      console.error(\"Error loading proposals:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'draft': return <Clock className=\"w-4 h-4\" />;\n      case 'sent': return <Send className=\"w-4 h-4\" />;\n      case 'accepted': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'rejected': return <XCircle className=\"w-4 h-4\" />;\n      default: return <FileText className=\"w-4 h-4\" />;\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'draft': return 'bg-slate-100 text-slate-700 border-slate-200';\n      case 'sent': return 'bg-blue-100 text-blue-700 border-blue-200';\n      case 'accepted': return 'bg-green-100 text-green-700 border-green-200';\n      case 'rejected': return 'bg-red-100 text-red-700 border-red-200';\n      default: return 'bg-slate-100 text-slate-700 border-slate-200';\n    }\n  };\n\n  const totalValue = proposals.reduce((sum, proposal) => sum + (proposal.total_cost || 0), 0);\n  const acceptedValue = proposals\n    .filter(p => p.status === 'accepted')\n    .reduce((sum, proposal) => sum + (proposal.total_cost || 0), 0);\n\n  return (\n    <div className=\"p-6 md:p-8 space-y-8\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Header */}\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center gap-6\">\n          <div>\n            <h1 className=\"text-4xl font-bold text-slate-800 mb-2\">\n              Proposal Dashboard\n            </h1>\n            <p className=\"text-slate-600\">\n              Manage and track your professional proposals\n            </p>\n          </div>\n          <Link to={createPageUrl(\"CreateProposal\")}>\n            <Button className=\"bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300\">\n              <Plus className=\"w-5 h-5 mr-2\" />\n              Create New Proposal\n            </Button>\n          </Link>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card className=\"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">\n                Total Proposals\n              </CardTitle>\n              <FileText className=\"h-4 w-4 text-indigo-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-800\">\n                {proposals.length}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">\n                Total Value\n              </CardTitle>\n              <DollarSign className=\"h-4 w-4 text-green-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-800\">\n                ${totalValue.toLocaleString()}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">\n                Accepted Value\n              </CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-purple-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-800\">\n                ${acceptedValue.toLocaleString()}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"glass-effect border-white/20 hover:shadow-lg transition-all duration-300\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-slate-600\">\n                Success Rate\n              </CardTitle>\n              <CheckCircle className=\"h-4 w-4 text-emerald-500\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-slate-800\">\n                {proposals.length > 0\n                  ? Math.round((proposals.filter(p => p.status === 'accepted').length / proposals.length) * 100)\n                  : 0}%\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Proposals List */}\n        <Card className=\"glass-effect border-white/20\">\n          <CardHeader>\n            <CardTitle className=\"text-xl font-semibold text-slate-800\">\n              Recent Proposals\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            {isLoading ? (\n              <div className=\"space-y-4\">\n                {[...Array(3)].map((_, i) => (\n                  <div key={i} className=\"animate-pulse\">\n                    <div className=\"h-24 bg-slate-200 rounded-lg\"></div>\n                  </div>\n                ))}\n              </div>\n            ) : proposals.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <FileText className=\"w-12 h-12 text-slate-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-slate-600 mb-2\">\n                  No proposals yet\n                </h3>\n                <p className=\"text-slate-500 mb-6\">\n                  Create your first professional proposal to get started\n                </p>\n                <Link to={createPageUrl(\"CreateProposal\")}>\n                  <Button className=\"bg-gradient-to-r from-indigo-500 to-purple-600 text-white\">\n                    <Plus className=\"w-4 h-4 mr-2\" />\n                    Create First Proposal\n                  </Button>\n                </Link>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {proposals.map((proposal, index) => (\n                  <div\n                    key={proposal.id}\n                    className=\"bg-white/60 rounded-xl border border-white/20 hover:shadow-lg transition-all duration-300 overflow-hidden\"\n                  >\n                    {proposal.cover_image_url && (\n                      <div className=\"h-40 bg-slate-200\">\n                        <img\n                          src={proposal.cover_image_url}\n                          alt=\"Proposal Cover\"\n                          className=\"w-full h-full object-cover\"\n                        />\n                      </div>\n                    )}\n                    <div className=\"p-6\">\n                      <div className=\"flex items-center justify-between gap-3 mb-2\">\n                        <h3 className=\"font-semibold text-slate-800 text-lg truncate\" title={proposal.title}>\n                          {proposal.title}\n                        </h3>\n                        <Badge className={`${getStatusColor(proposal.status)} border`}>\n                          {getStatusIcon(proposal.status)}\n                          <span className=\"ml-1 capitalize\">{proposal.status}</span>\n                        </Badge>\n                      </div>\n                      <div className=\"flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-slate-600\">\n                        <span className=\"flex items-center gap-1\">\n                          <Calendar className=\"w-4 h-4\" />\n                          {formatDate(proposal.created_date)}\n                        </span>\n                        <span className=\"truncate\">Client: {proposal.recipient_company || proposal.recipient_name}</span>\n                        {proposal.total_cost && (\n                          <span className=\"flex items-center gap-1 font-medium\">\n                            <DollarSign className=\"w-4 h-4\" />\n                            {proposal.total_cost.toLocaleString()}\n                          </span>\n                        )}\n                      </div>\n                      <div className=\"flex items-center gap-2 mt-4 pt-4 border-t border-white/20\">\n                        <Button variant=\"outline\" size=\"sm\" className=\"hover:bg-white/80\">\n                          <Eye className=\"w-4 h-4 mr-1\" />\n                          View\n                        </Button>\n                        <Button variant=\"outline\" size=\"sm\" className=\"hover:bg-white/80\">\n                          <Edit className=\"w-4 h-4 mr-1\" />\n                          Edit\n                        </Button>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,aAAa,EAAEC,UAAU,QAAQ,UAAU;AACpD,SACEC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,KAAK,QACA,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd8B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,IAAI,GAAG,MAAM9B,QAAQ,CAAC+B,IAAI,CAAC,eAAe,CAAC;MACjDL,YAAY,CAACI,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRJ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMM,aAAa,GAAIC,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,oBAAOb,OAAA,CAACF,KAAK;UAACgB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD,KAAK,MAAM;QAAE,oBAAOlB,OAAA,CAACL,IAAI;UAACmB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD,KAAK,UAAU;QAAE,oBAAOlB,OAAA,CAACJ,WAAW;UAACkB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,UAAU;QAAE,oBAAOlB,OAAA,CAACH,OAAO;UAACiB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD;QAAS,oBAAOlB,OAAA,CAACX,QAAQ;UAACyB,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,cAAc,GAAIN,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,8CAA8C;MACnE,KAAK,MAAM;QAAE,OAAO,2CAA2C;MAC/D,KAAK,UAAU;QAAE,OAAO,8CAA8C;MACtE,KAAK,UAAU;QAAE,OAAO,wCAAwC;MAChE;QAAS,OAAO,8CAA8C;IAChE;EACF,CAAC;EAED,MAAMO,UAAU,GAAGjB,SAAS,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,IAAIC,QAAQ,CAACC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3F,MAAMC,aAAa,GAAGtB,SAAS,CAC5BuB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,MAAM,KAAK,UAAU,CAAC,CACpCQ,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKD,GAAG,IAAIC,QAAQ,CAACC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAEjE,oBACExB,OAAA;IAAKc,SAAS,EAAC,sBAAsB;IAAAc,QAAA,eACnC5B,OAAA;MAAKc,SAAS,EAAC,mBAAmB;MAAAc,QAAA,gBAEhC5B,OAAA;QAAKc,SAAS,EAAC,6EAA6E;QAAAc,QAAA,gBAC1F5B,OAAA;UAAA4B,QAAA,gBACE5B,OAAA;YAAIc,SAAS,EAAC,wCAAwC;YAAAc,QAAA,EAAC;UAEvD;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlB,OAAA;YAAGc,SAAS,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAE9B;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlB,OAAA,CAACf,IAAI;UAAC4C,EAAE,EAAE3C,aAAa,CAAC,gBAAgB,CAAE;UAAA0C,QAAA,eACxC5B,OAAA,CAACrB,MAAM;YAACmC,SAAS,EAAC,2JAA2J;YAAAc,QAAA,gBAC3K5B,OAAA,CAACZ,IAAI;cAAC0B,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlB,OAAA;QAAKc,SAAS,EAAC,sDAAsD;QAAAc,QAAA,gBACnE5B,OAAA,CAACpB,IAAI;UAACkC,SAAS,EAAC,0EAA0E;UAAAc,QAAA,gBACxF5B,OAAA,CAAClB,UAAU;YAACgC,SAAS,EAAC,2DAA2D;YAAAc,QAAA,gBAC/E5B,OAAA,CAACjB,SAAS;cAAC+B,SAAS,EAAC,oCAAoC;cAAAc,QAAA,EAAC;YAE1D;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZlB,OAAA,CAACX,QAAQ;cAACyB,SAAS,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACblB,OAAA,CAACnB,WAAW;YAAA+C,QAAA,eACV5B,OAAA;cAAKc,SAAS,EAAC,mCAAmC;cAAAc,QAAA,EAC/CzB,SAAS,CAAC2B;YAAM;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPlB,OAAA,CAACpB,IAAI;UAACkC,SAAS,EAAC,0EAA0E;UAAAc,QAAA,gBACxF5B,OAAA,CAAClB,UAAU;YAACgC,SAAS,EAAC,2DAA2D;YAAAc,QAAA,gBAC/E5B,OAAA,CAACjB,SAAS;cAAC+B,SAAS,EAAC,oCAAoC;cAAAc,QAAA,EAAC;YAE1D;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZlB,OAAA,CAACT,UAAU;cAACuB,SAAS,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACblB,OAAA,CAACnB,WAAW;YAAA+C,QAAA,eACV5B,OAAA;cAAKc,SAAS,EAAC,mCAAmC;cAAAc,QAAA,GAAC,GAChD,EAACR,UAAU,CAACW,cAAc,CAAC,CAAC;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPlB,OAAA,CAACpB,IAAI;UAACkC,SAAS,EAAC,0EAA0E;UAAAc,QAAA,gBACxF5B,OAAA,CAAClB,UAAU;YAACgC,SAAS,EAAC,2DAA2D;YAAAc,QAAA,gBAC/E5B,OAAA,CAACjB,SAAS;cAAC+B,SAAS,EAAC,oCAAoC;cAAAc,QAAA,EAAC;YAE1D;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZlB,OAAA,CAACR,UAAU;cAACsB,SAAS,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACblB,OAAA,CAACnB,WAAW;YAAA+C,QAAA,eACV5B,OAAA;cAAKc,SAAS,EAAC,mCAAmC;cAAAc,QAAA,GAAC,GAChD,EAACH,aAAa,CAACM,cAAc,CAAC,CAAC;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPlB,OAAA,CAACpB,IAAI;UAACkC,SAAS,EAAC,0EAA0E;UAAAc,QAAA,gBACxF5B,OAAA,CAAClB,UAAU;YAACgC,SAAS,EAAC,2DAA2D;YAAAc,QAAA,gBAC/E5B,OAAA,CAACjB,SAAS;cAAC+B,SAAS,EAAC,oCAAoC;cAAAc,QAAA,EAAC;YAE1D;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACZlB,OAAA,CAACJ,WAAW;cAACkB,SAAS,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACblB,OAAA,CAACnB,WAAW;YAAA+C,QAAA,eACV5B,OAAA;cAAKc,SAAS,EAAC,mCAAmC;cAAAc,QAAA,GAC/CzB,SAAS,CAAC2B,MAAM,GAAG,CAAC,GACjBE,IAAI,CAACC,KAAK,CAAE9B,SAAS,CAACuB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,MAAM,KAAK,UAAU,CAAC,CAACiB,MAAM,GAAG3B,SAAS,CAAC2B,MAAM,GAAI,GAAG,CAAC,GAC5F,CAAC,EAAC,GACR;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlB,OAAA,CAACpB,IAAI;QAACkC,SAAS,EAAC,8BAA8B;QAAAc,QAAA,gBAC5C5B,OAAA,CAAClB,UAAU;UAAA8C,QAAA,eACT5B,OAAA,CAACjB,SAAS;YAAC+B,SAAS,EAAC,sCAAsC;YAAAc,QAAA,EAAC;UAE5D;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACblB,OAAA,CAACnB,WAAW;UAAA+C,QAAA,EACTvB,SAAS,gBACRL,OAAA;YAAKc,SAAS,EAAC,WAAW;YAAAc,QAAA,EACvB,CAAC,GAAGM,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBrC,OAAA;cAAac,SAAS,EAAC,eAAe;cAAAc,QAAA,eACpC5B,OAAA;gBAAKc,SAAS,EAAC;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC,GAD5CmB,CAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEN,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,GACJf,SAAS,CAAC2B,MAAM,KAAK,CAAC,gBACxB9B,OAAA;YAAKc,SAAS,EAAC,mBAAmB;YAAAc,QAAA,gBAChC5B,OAAA,CAACX,QAAQ;cAACyB,SAAS,EAAC;YAAuC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DlB,OAAA;cAAIc,SAAS,EAAC,yCAAyC;cAAAc,QAAA,EAAC;YAExD;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlB,OAAA;cAAGc,SAAS,EAAC,qBAAqB;cAAAc,QAAA,EAAC;YAEnC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlB,OAAA,CAACf,IAAI;cAAC4C,EAAE,EAAE3C,aAAa,CAAC,gBAAgB,CAAE;cAAA0C,QAAA,eACxC5B,OAAA,CAACrB,MAAM;gBAACmC,SAAS,EAAC,2DAA2D;gBAAAc,QAAA,gBAC3E5B,OAAA,CAACZ,IAAI;kBAAC0B,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,yBAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENlB,OAAA;YAAKc,SAAS,EAAC,uCAAuC;YAAAc,QAAA,EACnDzB,SAAS,CAACgC,GAAG,CAAC,CAACZ,QAAQ,EAAEe,KAAK,kBAC7BtC,OAAA;cAEEc,SAAS,EAAC,2GAA2G;cAAAc,QAAA,GAEpHL,QAAQ,CAACgB,eAAe,iBACvBvC,OAAA;gBAAKc,SAAS,EAAC,mBAAmB;gBAAAc,QAAA,eAChC5B,OAAA;kBACEwC,GAAG,EAAEjB,QAAQ,CAACgB,eAAgB;kBAC9BE,GAAG,EAAC,gBAAgB;kBACpB3B,SAAS,EAAC;gBAA4B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,eACDlB,OAAA;gBAAKc,SAAS,EAAC,KAAK;gBAAAc,QAAA,gBAClB5B,OAAA;kBAAKc,SAAS,EAAC,8CAA8C;kBAAAc,QAAA,gBAC3D5B,OAAA;oBAAIc,SAAS,EAAC,+CAA+C;oBAAC4B,KAAK,EAAEnB,QAAQ,CAACmB,KAAM;oBAAAd,QAAA,EACjFL,QAAQ,CAACmB;kBAAK;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACLlB,OAAA,CAAChB,KAAK;oBAAC8B,SAAS,EAAE,GAAGK,cAAc,CAACI,QAAQ,CAACV,MAAM,CAAC,SAAU;oBAAAe,QAAA,GAC3DhB,aAAa,CAACW,QAAQ,CAACV,MAAM,CAAC,eAC/Bb,OAAA;sBAAMc,SAAS,EAAC,iBAAiB;sBAAAc,QAAA,EAAEL,QAAQ,CAACV;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNlB,OAAA;kBAAKc,SAAS,EAAC,oEAAoE;kBAAAc,QAAA,gBACjF5B,OAAA;oBAAMc,SAAS,EAAC,yBAAyB;oBAAAc,QAAA,gBACvC5B,OAAA,CAACV,QAAQ;sBAACwB,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC/B/B,UAAU,CAACoC,QAAQ,CAACoB,YAAY,CAAC;kBAAA;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACPlB,OAAA;oBAAMc,SAAS,EAAC,UAAU;oBAAAc,QAAA,GAAC,UAAQ,EAACL,QAAQ,CAACqB,iBAAiB,IAAIrB,QAAQ,CAACsB,cAAc;kBAAA;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAChGK,QAAQ,CAACC,UAAU,iBAClBxB,OAAA;oBAAMc,SAAS,EAAC,qCAAqC;oBAAAc,QAAA,gBACnD5B,OAAA,CAACT,UAAU;sBAACuB,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACjCK,QAAQ,CAACC,UAAU,CAACO,cAAc,CAAC,CAAC;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlB,OAAA;kBAAKc,SAAS,EAAC,4DAA4D;kBAAAc,QAAA,gBACzE5B,OAAA,CAACrB,MAAM;oBAACmE,OAAO,EAAC,SAAS;oBAACC,IAAI,EAAC,IAAI;oBAACjC,SAAS,EAAC,mBAAmB;oBAAAc,QAAA,gBAC/D5B,OAAA,CAACP,GAAG;sBAACqB,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAElC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlB,OAAA,CAACrB,MAAM;oBAACmE,OAAO,EAAC,SAAS;oBAACC,IAAI,EAAC,IAAI;oBAACjC,SAAS,EAAC,mBAAmB;oBAAAc,QAAA,gBAC/D5B,OAAA,CAACN,IAAI;sBAACoB,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAEnC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA7CDK,QAAQ,CAACyB,EAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8Cb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAChB,EAAA,CAzNuBD,SAAS;AAAAgD,EAAA,GAAThD,SAAS;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}