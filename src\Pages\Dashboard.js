import React, { useState, useEffect } from "react";
import Proposal from "../Entities/Proposal";
import { Button } from "../components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { Link } from "react-router-dom";
import { createPageUrl, formatDate } from "../utils";
import {
  Plus,
  FileText,
  Calendar,
  DollarSign,
  TrendingUp,
  Eye,
  Edit,
  Send,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";

export default function Dashboard() {
  const [proposals, setProposals] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadProposals();
  }, []);

  const loadProposals = async () => {
    try {
      const data = await Proposal.list("-created_date");
      setProposals(data);
    } catch (error) {
      console.error("Error loading proposals:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'draft': return <Clock className="w-4 h-4" />;
      case 'sent': return <Send className="w-4 h-4" />;
      case 'accepted': return <CheckCircle className="w-4 h-4" />;
      case 'rejected': return <XCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'draft': return 'bg-slate-100 text-slate-700 border-slate-200';
      case 'sent': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'accepted': return 'bg-green-100 text-green-700 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-slate-100 text-slate-700 border-slate-200';
    }
  };

  const totalValue = proposals.reduce((sum, proposal) => sum + (proposal.total_cost || 0), 0);
  const acceptedValue = proposals
    .filter(p => p.status === 'accepted')
    .reduce((sum, proposal) => sum + (proposal.total_cost || 0), 0);

  return (
    <div className="p-6 md:p-8 space-y-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6">
          <div>
            <h1 className="text-4xl font-bold text-slate-800 mb-2">
              Proposal Dashboard
            </h1>
            <p className="text-slate-600">
              Manage and track your professional proposals
            </p>
          </div>
          <Link to={createPageUrl("CreateProposal")}>
            <Button className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
              <Plus className="w-5 h-5 mr-2" />
              Create New Proposal
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="glass-effect border-white/20 hover:shadow-lg transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Total Proposals
              </CardTitle>
              <FileText className="h-4 w-4 text-indigo-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800">
                {proposals.length}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-white/20 hover:shadow-lg transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Total Value
              </CardTitle>
              <DollarSign className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800">
                ${totalValue.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-white/20 hover:shadow-lg transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Accepted Value
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800">
                ${acceptedValue.toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card className="glass-effect border-white/20 hover:shadow-lg transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                Success Rate
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-emerald-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800">
                {proposals.length > 0
                  ? Math.round((proposals.filter(p => p.status === 'accepted').length / proposals.length) * 100)
                  : 0}%
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Proposals List */}
        <Card className="glass-effect border-white/20">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-slate-800">
              Recent Proposals
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-24 bg-slate-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : proposals.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-600 mb-2">
                  No proposals yet
                </h3>
                <p className="text-slate-500 mb-6">
                  Create your first professional proposal to get started
                </p>
                <Link to={createPageUrl("CreateProposal")}>
                  <Button className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white">
                    <Plus className="w-4 h-4 mr-2" />
                    Create First Proposal
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {proposals.map((proposal, index) => (
                  <div
                    key={proposal.id}
                    className="bg-white/60 rounded-xl border border-white/20 hover:shadow-lg transition-all duration-300 overflow-hidden"
                  >
                    {proposal.cover_image_url && (
                      <div className="h-40 bg-slate-200">
                        <img
                          src={proposal.cover_image_url}
                          alt="Proposal Cover"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <div className="p-6">
                      <div className="flex items-center justify-between gap-3 mb-2">
                        <h3 className="font-semibold text-slate-800 text-lg truncate" title={proposal.title}>
                          {proposal.title}
                        </h3>
                        <Badge className={`${getStatusColor(proposal.status)} border`}>
                          {getStatusIcon(proposal.status)}
                          <span className="ml-1 capitalize">{proposal.status}</span>
                        </Badge>
                      </div>
                      <div className="flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-slate-600">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          {formatDate(proposal.created_date)}
                        </span>
                        <span className="truncate">Client: {proposal.recipient_company || proposal.recipient_name}</span>
                        {proposal.total_cost && (
                          <span className="flex items-center gap-1 font-medium">
                            <DollarSign className="w-4 h-4" />
                            {proposal.total_cost.toLocaleString()}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-4 pt-4 border-t border-white/20">
                        <Button variant="outline" size="sm" className="hover:bg-white/80">
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-white/80">
                          <Edit className="w-4 h-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}