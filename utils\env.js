// Utility functions for environment variables

// Get environment variable with fallback
export const getEnv = (key, fallback = '') => {
  if (typeof window !== 'undefined') {
    return process.env[`NEXT_PUBLIC_${key}`] || fallback;
  }
  return process.env[key] || fallback;
};

// Check if a feature flag is enabled
export const isFeatureEnabled = (featureName) => {
  const value = getEnv(`ENABLE_${featureName.toUpperCase()}`, 'false');
 