import React, { useState } from "react";

export default function CoverImageSection({ value = {}, onChange }) {
  const [coverImage, setCoverImage] = useState({
    file: value.file || null,
    preview: value.preview || null,
    prompt: value.prompt || "",
    isGenerating: false
  });

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const updatedImage = {
          ...coverImage,
          file: file,
          preview: event.target.result
        };
        setCoverImage(updatedImage);
        onChange && onChange(updatedImage);
      };
      
      reader.readAsDataURL(file);
    }
  };

  const handlePromptChange = (e) => {
    const updatedImage = { ...coverImage, prompt: e.target.value };
    setCoverImage(updatedImage);
    onChange && onChange(updatedImage);
  };

  const handleGenerateImage = async () => {
    if (!coverImage.prompt) {
      alert("Please enter a prompt for image generation");
      return;
    }
    
    setCoverImage({ ...coverImage, isGenerating: true });
    
    try {
      // In a real implementation, you would call your AI image generation API
      // const response = await fetch('/api/generate-image', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ prompt: coverImage.prompt })
      // });
      // const data = await response.json();
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock image generation - in a real app, this would be the URL from your API
      const mockImageUrl = "https://placehold.co/600x400/667eea/ffffff?text=AI+Generated+Image";
      
      const updatedImage = {
        ...coverImage,
        preview: mockImageUrl,
        isGenerating: false
      };
      
      setCoverImage(updatedImage);
      onChange && onChange(updatedImage);
    } catch (error) {
      console.error("Image generation failed:", error);
      setCoverImage({ ...coverImage, isGenerating: false });
      alert("Failed to generate image. Please try again.");
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const updatedImage = {
          ...coverImage,
          file: file,
          preview: event.target.result
        };
        setCoverImage(updatedImage);
        onChange && onChange(updatedImage);
      };
      
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="section p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-3">Cover Image</h2>
      <p className="text-gray-600 mb-4">Add a professional cover image to your proposal</p>
      
      <div className="space-y-6">
        {/* Image Upload Area */}
        <div 
          className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer"
          onClick={() => document.getElementById("cover-image-upload").click()}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {coverImage.preview ? (
            <div className="flex flex-col items-center">
              <img 
                src={coverImage.preview} 
                alt="Cover preview" 
                className="max-h-64 max-w-full mb-4 rounded"
              />
              <p className="text-sm text-gray-500">Click or drag to replace image</p>
            </div>
          ) : (
            <div className="py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <p className="mt-2">Drag and drop an image or click to upload</p>
            </div>
          )}
          <input 
            id="cover-image-upload" 
            type="file" 
            className="hidden" 
            accept="image/*" 
            onChange={handleFileChange}
          />
        </div>
        
        {/* AI Image Generation */}
        <div className="border-t pt-6">
          <h3 className="font-medium mb-3">Or generate with AI</h3>
          <div className="flex gap-2">
            <input
              type="text"
              className="flex-1 p-2 border rounded"
              placeholder="Describe the image you want to generate..."
              value={coverImage.prompt}
              onChange={handlePromptChange}
            />
            <button
              onClick={handleGenerateImage}
              disabled={coverImage.isGenerating}
              className={`px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 ${
                coverImage.isGenerating ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {coverImage.isGenerating ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </span>
              ) : "Generate"}
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Powered by AI. Enter a description of the image you want for your proposal.
          </p>
        </div>
      </div>
    </div>
  );
}
