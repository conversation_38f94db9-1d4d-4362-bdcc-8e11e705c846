import React, { useState } from "react";
import BasicInfoSection from "../Components/BasicInfoSection";
import CompanyInfoSection from "../Components/CompanyInfoSection";
import RecipientInfoSection from "../Components/RecipientInfoSection";
import ProjectDetailsSection from "../Components/ProjectDetailsSection";
import ServicesSection from "../Components/ServicesSection";
import MilestonesSection from "../Components/MilestonesSection";
import TermsSection from "../Components/TermsSection";
import ConclusionSection from "../Components/ConclusionSection";
import CoverImageSection from "../Components/CoverImageSection";
import Proposal from "../Entities/Proposal";

export default function CreateProposal() {
  const [proposal, setProposal] = useState(new Proposal());
  const [activeTab, setActiveTab] = useState("edit"); // "edit" or "preview"
  const [showAIReviewModal, setShowAIReviewModal] = useState(false);
  const [isReviewing, setIsReviewing] = useState(false);
  const [reviewComplete, setReviewComplete] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState(null);

  const handleSectionChange = (sectionName, data) => {
    setProposal(prev => {
      const updated = {...prev};
      updated.sections = updated.sections || {};
      updated.sections[sectionName] = data;
      return updated;
    });
  };

  const handleSave = () => {
    // Show AI review confirmation modal
    setShowAIReviewModal(true);
  };

  const handleAIReview = async () => {
    setIsReviewing(true);
    
    try {
      // Simulate AI review process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, you would call your AI service here
      // const response = await fetch('/api/ai-review', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(proposal)
      // });
      // const data = await response.json();
      
      // Mock AI suggestions
      const mockSuggestions = {
        coverImage: {
          prompt: "A professional business proposal with abstract blue and purple gradient elements, featuring a handshake silhouette symbolizing partnership",
          rationale: "This image conveys professionalism and partnership, aligning with your proposal's tone and purpose."
        },
        contentImprovements: [
          "Consider adding more specific metrics in your milestones section to make progress more measurable.",
          "The introduction could benefit from a stronger value proposition statement.",
          "Your pricing structure might be more compelling with tiered options."
        ]
      };
      
      setAiSuggestions(mockSuggestions);
      setReviewComplete(true);
    } catch (error) {
      console.error("AI review failed:", error);
    } finally {
      setIsReviewing(false);
    }
  };

  const handleCloseModal = () => {
    setShowAIReviewModal(false);
    setReviewComplete(false);
    setAiSuggestions(null);
  };

  const handleApplySuggestions = () => {
    // Here you would apply the AI suggestions to your proposal
    // For now, we'll just close the modal and save
    handleCloseModal();
    console.log("Saving proposal:", proposal);
    alert("Proposal saved successfully with AI recommendations!");
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create Proposal</h1>
        <div className="flex gap-3">
          <button 
            onClick={() => setActiveTab("edit")}
            className={`px-4 py-2 rounded ${activeTab === "edit" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
          >
            Edit
          </button>
          <button 
            onClick={() => setActiveTab("preview")}
            className={`px-4 py-2 rounded ${activeTab === "preview" ? "bg-blue-500 text-white" : "bg-gray-200"}`}
          >
            Preview
          </button>
          <button 
            onClick={handleSave}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Save Proposal
          </button>
        </div>
      </div>

      {activeTab === "edit" ? (
        <div className="space-y-6">
          <BasicInfoSection 
            value={proposal.sections?.basicInfo} 
            onChange={(data) => handleSectionChange("basicInfo", data)} 
          />
          <CompanyInfoSection 
            value={proposal.sections?.companyInfo} 
            onChange={(data) => handleSectionChange("companyInfo", data)} 
          />
          <RecipientInfoSection 
            value={proposal.sections?.recipientInfo} 
            onChange={(data) => handleSectionChange("recipientInfo", data)} 
          />
          <ProjectDetailsSection 
            value={proposal.sections?.projectDetails} 
            onChange={(data) => handleSectionChange("projectDetails", data)} 
          />
          <ServicesSection 
            value={proposal.sections?.services} 
            onChange={(data) => handleSectionChange("services", data)} 
          />
          <MilestonesSection 
            value={proposal.sections?.milestones} 
            onChange={(data) => handleSectionChange("milestones", data)} 
          />
          <TermsSection 
            value={proposal.sections?.terms} 
            onChange={(data) => handleSectionChange("terms", data)} 
          />
          <ConclusionSection 
            value={proposal.sections?.conclusion} 
            onChange={(data) => handleSectionChange("conclusion", data)} 
          />
          <CoverImageSection 
            value={proposal.sections?.coverImage} 
            onChange={(data) => handleSectionChange("coverImage", data)} 
          />
        </div>
      ) : (
        <div className="bg-white p-8 rounded-lg shadow-md max-w-4xl mx-auto">
          {/* Proposal Preview - formatted according to your template */}
          <div className="proposal-preview">
            {/* Company Header */}
            <div className="mb-8">
              <p>{proposal.sections?.companyInfo?.name || "[Your Company's Name]"}</p>
              <p>{proposal.sections?.companyInfo?.address || "[Your Company's Address]"}</p>
              <p>{proposal.sections?.companyInfo?.cityStateZip || "[City, State, ZIP Code]"}</p>
              <p>{proposal.sections?.companyInfo?.email || "[Email Address]"}</p>
              <p>{proposal.sections?.companyInfo?.phone || "[Phone Number]"}</p>
              <p>{proposal.sections?.basicInfo?.date || "[Date]"}</p>
            </div>
            
            {/* Recipient Info */}
            <div className="mb-8">
              <p>{proposal.sections?.recipientInfo?.name || "[Recipient's Name]"}</p>
              <p>{proposal.sections?.recipientInfo?.company || "[Recipient's Company]"}</p>
              <p>{proposal.sections?.recipientInfo?.address || "[Recipient's Address]"}</p>
              <p>{proposal.sections?.recipientInfo?.cityStateZip || "[City, State, ZIP Code]"}</p>
            </div>
            
            {/* Subject Line */}
            <div className="mb-8">
              <p className="font-bold">Subject: Proposal for {proposal.sections?.basicInfo?.serviceName || "[Service Name]"}</p>
            </div>
            
            {/* Greeting */}
            <div className="mb-8">
              <p>Dear {proposal.sections?.recipientInfo?.name || "[Recipient's Name]"},</p>
              <p className="mt-4">
                We are pleased to submit this proposal to provide {proposal.sections?.basicInfo?.serviceName || "[Service Name]"} for {proposal.sections?.recipientInfo?.company || "[Recipient's Company]"}. 
                We believe that our expertise and experience make us an ideal partner to meet your needs and exceed your expectations.
              </p>
            </div>
            
            {/* Main Content Sections */}
            <div className="mb-8">
              <h2 className="text-xl font-bold mb-2">1. Introduction</h2>
              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.projectDetails?.introduction || "Purpose: Briefly describe the purpose of the proposal.<br>Background: Provide a brief background of your company and its qualifications.<br>Objectives: Outline the objectives of the proposed service." }} />
            </div>
            
            <div className="mb-8">
              <h2 className="text-xl font-bold mb-2">2. Scope of Services</h2>
              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.services?.description || "Services Offered: Detailed description of the services to be provided.<br>Deliverables: List of specific deliverables that the client can expect.<br>Exclusions: Any services or deliverables that are explicitly not included." }} />
              
              {proposal.sections?.services?.items && proposal.sections.services.items.length > 0 && (
                <table className="w-full border-collapse mt-4">
                  <thead>
                    <tr>
                      <th className="border p-2 text-left">Item</th>
                      <th className="border p-2 text-left">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {proposal.sections.services.items.map((service, index) => (
                      <tr key={index}>
                        <td className="border p-2">{service.name}</td>
                        <td className="border p-2">{service.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
            
            <div className="mb-8">
              <h2 className="text-xl font-bold mb-2">3. Approach and Methodology</h2>
              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.projectDetails?.methodology || "Methodology: Describe the approach and methods that will be used to deliver the services.<br>Project Plan: Outline the project plan, including key milestones and timelines." }} />
            </div>
            
            <div className="mb-8">
              <h2 className="text-xl font-bold mb-2">4. Timeline</h2>
              {proposal.sections?.milestones && proposal.sections.milestones.length > 0 && (
                <table className="w-full border-collapse mt-4">
                  <thead>
                    <tr>
                      <th className="border p-2 text-left">Milestone</th>
                      <th className="border p-2 text-left">Description</th>
                      <th className="border p-2 text-left">Deadline</th>
                    </tr>
                  </thead>
                  <tbody>
                    {proposal.sections.milestones.map((milestone, index) => (
                      <tr key={index}>
                        <td className="border p-2">{milestone.title}</td>
                        <td className="border p-2">{milestone.description}</td>
                        <td className="border p-2">{milestone.deadline}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
            
            {/* Additional sections would continue here */}
            
            <div className="mb-8">
              <h2 className="text-xl font-bold mb-2">8. Conclusion</h2>
              <div dangerouslySetInnerHTML={{ __html: proposal.sections?.conclusion?.text || "Summary: Summarize the proposal and reiterate the benefits of the proposed services.<br>Call to Action: Encourage the client to take the next steps, such as contacting your company to discuss the proposal further." }} />
              <p className="mt-4">
                We look forward to the opportunity to work with {proposal.sections?.recipientInfo?.company || "[Recipient's Company]"} and provide {proposal.sections?.basicInfo?.serviceName || "[Service Name]"}. 
                Please feel free to contact us if you have any questions or need further information.
              </p>
            </div>
            
            {/* Signature */}
            <div className="mt-12">
              <p>Sincerely,</p>
              <p className="mt-6">{proposal.sections?.companyInfo?.contactName || "[Your Name]"}</p>
              <p>{proposal.sections?.companyInfo?.contactTitle || "[Your Position]"}</p>
              <p>{proposal.sections?.companyInfo?.name || "[Your Company's Name]"}</p>
            </div>
          </div>
        </div>
      )}

      {/* AI Review Modal */}
      {showAIReviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h2 className="text-xl font-bold mb-4">
              {!reviewComplete ? "AI Review" : "AI Suggestions"}
            </h2>
            
            {!reviewComplete ? (
              <>
                <p className="mb-4">
                  Would you like our AI to review your proposal and suggest improvements?
                </p>
                
                {isReviewing ? (
                  <div className="flex flex-col items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                    <p>Analyzing your proposal...</p>
                  </div>
                ) : (
                  <div className="flex justify-end gap-3">
                    <button
                      onClick={handleCloseModal}
                      className="px-4 py-2 border rounded hover:bg-gray-100"
                    >
                      Skip
                    </button>
                    <button
                      onClick={handleAIReview}
                      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                      Review with AI
                    </button>
                  </div>
                )}
              </>
            ) : (
              <>
                <div className="mb-6">
                  <h3 className="font-semibold mb-2">Cover Image Suggestion:</h3>
                  <div className="bg-gray-50 p-3 rounded mb-2">
                    <p className="text-sm font-medium">Prompt:</p>
                    <p className="text-sm">{aiSuggestions.coverImage.prompt}</p>
                  </div>
                  <p className="text-sm text-gray-600">{aiSuggestions.coverImage.rationale}</p>
                </div>
                
                <div className="mb-6">
                  <h3 className="font-semibold mb-2">Content Improvements:</h3>
                  <ul className="list-disc pl-5 space-y-1">
                    {aiSuggestions.contentImprovements.map((suggestion, index) => (
                      <li key={index} className="text-sm">{suggestion}</li>
                    ))}
                  </ul>
                </div>
                
                <div className="flex justify-end gap-3">
                  <button
                    onClick={handleCloseModal}
                    className="px-4 py-2 border rounded hover:bg-gray-100"
                  >
                    Ignore
                  </button>
                  <button
                    onClick={handleApplySuggestions}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    Apply Suggestions
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}



